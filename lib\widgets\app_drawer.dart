import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/debtor_provider.dart';
import '../providers/settings_provider.dart';
import '../screens/settings_screen.dart';
import '../screens/about_screen.dart';
import '../screens/advanced_import_export_screen.dart';
import '../screens/qr_demo_screen.dart';
import '../screens/due_dates_screen.dart';
import '../utils/app_theme.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          // Header
          _buildDrawerHeader(context),

          // Menu Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildMenuItem(
                  context,
                  icon: Icons.home,
                  title: 'الرئيسية',
                  subtitle: 'قائمة المدينين',
                  onTap: () => Navigator.of(context).pop(),
                ),

                const Divider(),

                Consumer<DebtorProvider>(
                  builder: (context, provider, child) {
                    final totalDebtors = provider.debtors.length;
                    final totalDebt = provider.debtors.fold<double>(
                      0.0,
                      (sum, debtor) =>
                          sum + provider.calculateRemainingDebt(debtor.id),
                    );

                    return _buildStatsSection(context, totalDebtors, totalDebt);
                  },
                ),

                const Divider(),

                _buildMenuItem(
                  context,
                  icon: Icons.settings,
                  title: 'الإعدادات',
                  subtitle: 'تخصيص التطبيق',
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const SettingsScreen(),
                      ),
                    );
                  },
                ),

                _buildMenuItem(
                  context,
                  icon: Icons.schedule,
                  title: 'تواريخ التسديد',
                  subtitle: 'إدارة الديون المتأخرة والتذكيرات',
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const DueDatesScreen(),
                      ),
                    );
                  },
                ),

                _buildMenuItem(
                  context,
                  icon: Icons.cloud_sync,
                  title: 'التصدير والاستيراد المتطور',
                  subtitle: 'مزامنة سحابية وتصدير متقدم',
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder:
                            (context) => const AdvancedImportExportScreen(),
                      ),
                    );
                  },
                ),

                _buildMenuItem(
                  context,
                  icon: Icons.qr_code,
                  title: 'نظام QR الذكي',
                  subtitle: 'إنشاء ومسح رموز QR',
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const QRDemoScreen(),
                      ),
                    );
                  },
                ),

                _buildMenuItem(
                  context,
                  icon: Icons.info,
                  title: 'حول التطبيق',
                  subtitle: 'معلومات التطبيق والمطور',
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AboutScreen(),
                      ),
                    );
                  },
                ),

                const Divider(),

                _buildMenuItem(
                  context,
                  icon: Icons.share,
                  title: 'مشاركة التطبيق',
                  subtitle: 'شارك مع الأصدقاء',
                  onTap: () => _shareApp(context),
                ),

                _buildMenuItem(
                  context,
                  icon: Icons.star_rate,
                  title: 'تقييم التطبيق',
                  subtitle: 'قيم التطبيق في المتجر',
                  onTap: () => _rateApp(context),
                ),
              ],
            ),
          ),

          // Footer
          _buildDrawerFooter(context),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: AppTheme.primaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // App Icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: const Icon(
                  Icons.account_balance_wallet,
                  size: 30,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 16),

              // App Name
              Text(
                'متتبع الديون',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 4),

              // Subtitle
              Text(
                'إدارة ذكية للديون',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),

              const Spacer(),

              // Version
              Consumer<SettingsProvider>(
                builder: (context, settings, child) {
                  return Text(
                    'الإصدار 1.0.0',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: (iconColor ?? AppTheme.primaryColor).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: iconColor ?? AppTheme.primaryColor, size: 20),
      ),
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
      subtitle: Text(
        subtitle,
        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    );
  }

  Widget _buildStatsSection(
    BuildContext context,
    int totalDebtors,
    double totalDebt,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات سريعة',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),

          const SizedBox(height: 12),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildStatItem(
                context,
                'المدينون',
                totalDebtors.toString(),
                Icons.people,
              ),
              _buildStatItem(
                context,
                'إجمالي الديون',
                '${totalDebt.toStringAsFixed(0)} ريال',
                Icons.account_balance_wallet,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, size: 16, color: AppTheme.primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildDrawerFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey[300]!, width: 1)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'تم التطوير بواسطة حسن',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(width: 4),
          Icon(Icons.favorite, size: 12, color: Colors.red[400]),
        ],
      ),
    );
  }

  void _showBackupDialog(BuildContext context) {
    Navigator.of(context).pop();
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('النسخ الاحتياطي'),
            content: const Text('هذه الميزة ستكون متاحة في التحديث القادم'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  void _shareApp(BuildContext context) {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تنفيذ ميزة المشاركة قريباً')),
    );
  }

  void _rateApp(BuildContext context) {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('سيتم فتح المتجر قريباً')));
  }
}
