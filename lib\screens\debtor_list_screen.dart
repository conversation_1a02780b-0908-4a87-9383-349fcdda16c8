import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/debtor_provider.dart';
import '../providers/settings_provider.dart';
import '../widgets/debtor_card.dart';
import '../widgets/debtor_shimmer.dart';
import '../widgets/common_widgets.dart' as common_widgets;
import '../widgets/enhanced_ui_components.dart';
import '../widgets/responsive_layout.dart';
import '../widgets/app_drawer.dart';
import '../widgets/animated_search_bar.dart';
import '../widgets/professional_greeting.dart';
import '../utils/ui_improvements.dart';
import '../models/debtor.dart';
import 'add_debtor_screen.dart';
import 'debtor_detail_screen.dart';
import 'settings_screen.dart';
import '../services/interactive_tutorial_service.dart';

class DebtorListScreen extends StatefulWidget {
  final bool showAppBar;

  const DebtorListScreen({super.key, this.showAppBar = true});

  @override
  State<DebtorListScreen> createState() => _DebtorListScreenState();
}

class _DebtorListScreenState extends State<DebtorListScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  SortOption _sortOption = SortOption.lastActivity;
  bool _sortAscending = false;

  // Global keys for tutorial
  final GlobalKey _searchButtonKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });

    // Register keys for tutorial
    WidgetsBinding.instance.addPostFrameCallback((_) {
      InteractiveTutorialService.registerWidgetKey(
        'search_button',
        _searchButtonKey,
      );
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return Scaffold(
          drawer: widget.showAppBar ? const AppDrawer() : null,
          appBar:
              widget.showAppBar
                  ? common_widgets.CustomAppBar(
                    title:
                        settings.language == AppLanguage.arabic
                            ? 'إدارة الديون'
                            : 'Debt Management',
                    actions: [
                      IconButton(
                        icon: const Icon(Icons.settings),
                        onPressed:
                            () => Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => const SettingsScreen(),
                              ),
                            ),
                        tooltip: 'الإعدادات',
                      ),
                      PopupMenuButton<SortOption>(
                        icon: const Icon(Icons.sort),
                        onSelected: (option) {
                          setState(() {
                            if (_sortOption == option) {
                              _sortAscending = !_sortAscending;
                            } else {
                              _sortOption = option;
                              _sortAscending = false;
                            }
                          });
                        },
                        itemBuilder:
                            (context) => [
                              PopupMenuItem(
                                value: SortOption.name,
                                child: Row(
                                  children: [
                                    const Icon(Icons.sort_by_alpha),
                                    const SizedBox(width: 8),
                                    const Text('الاسم'),
                                    if (_sortOption == SortOption.name)
                                      Icon(
                                        _sortAscending
                                            ? Icons.arrow_upward
                                            : Icons.arrow_downward,
                                      ),
                                  ],
                                ),
                              ),
                              PopupMenuItem(
                                value: SortOption.balance,
                                child: Row(
                                  children: [
                                    const Icon(Icons.account_balance_wallet),
                                    const SizedBox(width: 8),
                                    const Text('المبلغ المتبقي'),
                                    if (_sortOption == SortOption.balance)
                                      Icon(
                                        _sortAscending
                                            ? Icons.arrow_upward
                                            : Icons.arrow_downward,
                                      ),
                                  ],
                                ),
                              ),
                              PopupMenuItem(
                                value: SortOption.lastActivity,
                                child: Row(
                                  children: [
                                    const Icon(Icons.access_time),
                                    const SizedBox(width: 8),
                                    const Text('آخر نشاط'),
                                    if (_sortOption == SortOption.lastActivity)
                                      Icon(
                                        _sortAscending
                                            ? Icons.arrow_upward
                                            : Icons.arrow_downward,
                                      ),
                                  ],
                                ),
                              ),
                            ],
                      ),
                    ],
                  )
                  : null,
          body: Column(
            children: [
              // Professional Search Section
              Container(
                margin: const EdgeInsets.fromLTRB(20, 24, 20, 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Professional greeting
                    const ProfessionalGreeting(),

                    const SizedBox(height: 20),

                    // Enhanced search bar
                    AnimatedSearchBar(
                      key: _searchButtonKey,
                      controller: _searchController,
                      hintText: 'البحث عن مدين...',
                      onChanged: (value) {
                        // Search functionality will be handled by existing logic
                      },
                      onClear: () {
                        _searchController.clear();
                      },
                      width: MediaQuery.of(context).size.width - 40,
                    ),
                  ],
                ),
              ),

              // Debtors list
              Expanded(
                child: Consumer<DebtorProvider>(
                  builder: (context, provider, child) {
                    if (provider.isLoading) {
                      return const DebtorListShimmer(itemCount: 6);
                    }

                    if (provider.error != null) {
                      return common_widgets.ErrorWidget(
                        message: provider.error!,
                        onRetry: () => provider.loadData(),
                      );
                    }

                    final filteredDebtors = _getFilteredAndSortedDebtors(
                      provider,
                    );

                    if (filteredDebtors.isEmpty) {
                      return common_widgets.EmptyStateWidget(
                        icon:
                            _searchQuery.isNotEmpty
                                ? Icons.search_off
                                : Icons.person_add,
                        title:
                            _searchQuery.isNotEmpty
                                ? 'لا توجد نتائج'
                                : 'لا يوجد مدينون',
                        subtitle:
                            _searchQuery.isNotEmpty
                                ? 'لم يتم العثور على مدينين يطابقون البحث'
                                : 'ابدأ بإضافة مدين جديد لتتبع الديون',
                        action:
                            _searchQuery.isEmpty
                                ? ElevatedButton.icon(
                                  onPressed:
                                      () => _navigateToAddDebtor(context),
                                  icon: const Icon(Icons.add),
                                  label: const Text('إضافة مدين جديد'),
                                )
                                : null,
                      );
                    }

                    return RefreshIndicator(
                      onRefresh: () => provider.loadData(),
                      child: ListView.builder(
                        padding: const EdgeInsets.only(bottom: 80),
                        itemCount: filteredDebtors.length,
                        itemBuilder: (context, index) {
                          final debtor = filteredDebtors[index];
                          final remainingDebt = provider.calculateRemainingDebt(
                            debtor.id,
                          );
                          final totalItems = provider.getTotalItemsForDebtor(
                            debtor.id,
                          );
                          final totalPayments = provider
                              .getTotalPaymentsForDebtor(debtor.id);

                          return DebtorCard(
                            debtor: debtor,
                            remainingDebt: remainingDebt,
                            totalItems: totalItems,
                            totalPayments: totalPayments,
                            onTap:
                                () => _navigateToDebtorDetail(context, debtor),
                            onEdit:
                                () => _navigateToEditDebtor(context, debtor),
                            onDelete:
                                () => _showDeleteConfirmation(
                                  context,
                                  debtor,
                                  provider,
                                ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  List<Debtor> _getFilteredAndSortedDebtors(DebtorProvider provider) {
    List<Debtor> debtors = provider.searchDebtors(_searchQuery);

    switch (_sortOption) {
      case SortOption.name:
        debtors.sort(
          (a, b) =>
              _sortAscending
                  ? a.name.compareTo(b.name)
                  : b.name.compareTo(a.name),
        );
        break;
      case SortOption.balance:
        debtors =
            provider
                .getDebtorsSortedByBalance(ascending: _sortAscending)
                .where(
                  (debtor) =>
                      provider.searchDebtors(_searchQuery).contains(debtor),
                )
                .toList();
        break;
      case SortOption.lastActivity:
        debtors =
            provider
                .getDebtorsSortedByLastActivity(ascending: _sortAscending)
                .where(
                  (debtor) =>
                      provider.searchDebtors(_searchQuery).contains(debtor),
                )
                .toList();
        break;
    }

    return debtors;
  }

  void _navigateToAddDebtor(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AddDebtorScreen()));
  }

  void _navigateToEditDebtor(BuildContext context, Debtor debtor) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => AddDebtorScreen(debtor: debtor)),
    );
  }

  void _navigateToDebtorDetail(BuildContext context, Debtor debtor) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DebtorDetailScreen(debtor: debtor),
      ),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    Debtor debtor,
    DebtorProvider provider,
  ) async {
    final confirmed = await common_widgets.ConfirmationDialog.show(
      context,
      title: 'حذف المدين',
      content:
          'هل أنت متأكد من حذف "${debtor.name}"؟\nسيتم حذف جميع العناصر والمدفوعات المرتبطة به.',
      confirmText: 'حذف',
      confirmColor: Colors.red,
    );

    if (confirmed == true && context.mounted) {
      await provider.deleteDebtor(debtor.id);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف "${debtor.name}" بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }
}

enum SortOption { name, balance, lastActivity }
