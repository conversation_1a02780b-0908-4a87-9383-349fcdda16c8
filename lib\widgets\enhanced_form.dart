import 'package:flutter/material.dart';
import '../utils/ui_enhancement_service.dart';

/// Enhanced form widget that prevents overflow and provides better UX
class EnhancedForm extends StatefulWidget {
  final GlobalKey<FormState>? formKey;
  final List<Widget> children;
  final EdgeInsets? padding;
  final double spacing;
  final Widget? submitButton;
  final String? submitButtonText;
  final VoidCallback? onSubmit;
  final bool isLoading;
  final bool autovalidateMode;

  const EnhancedForm({
    super.key,
    this.formKey,
    required this.children,
    this.padding,
    this.spacing = 16,
    this.submitButton,
    this.submitButtonText,
    this.onSubmit,
    this.isLoading = false,
    this.autovalidateMode = false,
  });

  @override
  State<EnhancedForm> createState() => _EnhancedFormState();
}

class _EnhancedFormState extends State<EnhancedForm> {
  late GlobalKey<FormState> _formKey;

  @override
  void initState() {
    super.initState();
    _formKey = widget.formKey ?? GlobalKey<FormState>();
  }

  @override
  Widget build(BuildContext context) {
    return UIEnhancementService.safeWrapper(
      child: Form(
        key: _formKey,
        autovalidateMode: widget.autovalidateMode 
            ? AutovalidateMode.onUserInteraction 
            : AutovalidateMode.disabled,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ...widget.children.map((child) => Padding(
              padding: EdgeInsets.only(bottom: widget.spacing),
              child: child,
            )),
            if (widget.submitButton != null || widget.submitButtonText != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: widget.submitButton ?? 
                  UIEnhancementService.enhancedButton(
                    text: widget.submitButtonText ?? 'حفظ',
                    onPressed: widget.isLoading ? null : () {
                      if (_formKey.currentState?.validate() ?? false) {
                        widget.onSubmit?.call();
                      }
                    },
                    isLoading: widget.isLoading,
                    icon: Icons.save,
                  ),
              ),
          ],
        ),
      ),
      padding: widget.padding ?? const EdgeInsets.all(16),
    );
  }
}

/// Enhanced form field with consistent styling
class EnhancedFormField extends StatelessWidget {
  final String? label;
  final String? hint;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconPressed;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final TextInputType? keyboardType;
  final bool obscureText;
  final int? maxLines;
  final int? maxLength;
  final bool enabled;
  final bool required;

  const EnhancedFormField({
    super.key,
    this.label,
    this.hint,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
    this.controller,
    this.validator,
    this.onChanged,
    this.keyboardType,
    this.obscureText = false,
    this.maxLines,
    this.maxLength,
    this.enabled = true,
    this.required = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                UIEnhancementService.responsiveText(
                  label!,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                if (required)
                  const Text(
                    ' *',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
        UIEnhancementService.enhancedTextField(
          controller: controller,
          hintText: hint,
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
          onSuffixIconPressed: onSuffixIconPressed,
          validator: validator,
          onChanged: onChanged,
          keyboardType: keyboardType,
          obscureText: obscureText,
          maxLines: maxLines,
          enabled: enabled,
        ),
      ],
    );
  }
}

/// Enhanced dropdown field
class EnhancedDropdownField<T> extends StatelessWidget {
  final String? label;
  final String? hint;
  final IconData? prefixIcon;
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final void Function(T?)? onChanged;
  final String? Function(T?)? validator;
  final bool enabled;
  final bool required;

  const EnhancedDropdownField({
    super.key,
    this.label,
    this.hint,
    this.prefixIcon,
    this.value,
    required this.items,
    this.onChanged,
    this.validator,
    this.enabled = true,
    this.required = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                UIEnhancementService.responsiveText(
                  label!,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                if (required)
                  const Text(
                    ' *',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
        LayoutBuilder(
          builder: (context, constraints) {
            final isSmallScreen = constraints.maxWidth < 400;
            
            return DropdownButtonFormField<T>(
              value: value,
              items: items,
              onChanged: enabled ? onChanged : null,
              validator: validator,
              decoration: InputDecoration(
                hintText: hint,
                prefixIcon: prefixIcon != null
                    ? Icon(prefixIcon, size: isSmallScreen ? 20 : 24)
                    : null,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 12 : 16,
                  vertical: isSmallScreen ? 12 : 16,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                  borderSide: BorderSide(
                    color: Theme.of(context).primaryColor,
                    width: 2,
                  ),
                ),
              ),
              style: TextStyle(
                fontSize: isSmallScreen ? 14 : 16,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            );
          },
        ),
      ],
    );
  }
}

/// Enhanced date picker field
class EnhancedDateField extends StatelessWidget {
  final String? label;
  final String? hint;
  final IconData? prefixIcon;
  final DateTime? value;
  final void Function(DateTime?)? onChanged;
  final String? Function(DateTime?)? validator;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final bool enabled;
  final bool required;

  const EnhancedDateField({
    super.key,
    this.label,
    this.hint,
    this.prefixIcon,
    this.value,
    this.onChanged,
    this.validator,
    this.firstDate,
    this.lastDate,
    this.enabled = true,
    this.required = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                UIEnhancementService.responsiveText(
                  label!,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                if (required)
                  const Text(
                    ' *',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
        LayoutBuilder(
          builder: (context, constraints) {
            final isSmallScreen = constraints.maxWidth < 400;
            
            return TextFormField(
              readOnly: true,
              enabled: enabled,
              validator: validator != null 
                  ? (val) => validator!(value)
                  : null,
              decoration: InputDecoration(
                hintText: hint ?? 'اختر التاريخ',
                prefixIcon: Icon(
                  prefixIcon ?? Icons.calendar_today,
                  size: isSmallScreen ? 20 : 24,
                ),
                suffixIcon: const Icon(Icons.arrow_drop_down),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 12 : 16,
                  vertical: isSmallScreen ? 12 : 16,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                  borderSide: BorderSide(
                    color: Theme.of(context).primaryColor,
                    width: 2,
                  ),
                ),
              ),
              controller: TextEditingController(
                text: value != null 
                    ? '${value!.day}/${value!.month}/${value!.year}'
                    : '',
              ),
              onTap: enabled ? () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: value ?? DateTime.now(),
                  firstDate: firstDate ?? DateTime(2000),
                  lastDate: lastDate ?? DateTime(2100),
                );
                if (date != null) {
                  onChanged?.call(date);
                }
              } : null,
            );
          },
        ),
      ],
    );
  }
}
