import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import '../models/debtor.dart';
import '../models/debt_item.dart';
import '../models/payment.dart';
import '../services/database_service.dart';

/// خدمة QR الذكية لإنشاء ومشاركة إحصائيات المديونين
class SmartQRService {
  static const String _qrPrefix = 'DEBTOR_TRACKER_';
  static const String _version = '1.0';

  /// إنشاء QR لمدين محدد مع إحصائياته
  static Future<String> generateDebtorQR({
    required Debtor debtor,
    bool includeDetails = true,
    bool includePaymentHistory = false,
  }) async {
    try {
      final debtItems = await DatabaseService.getAllDebtItemsAsync();
      final payments = await DatabaseService.getAllPaymentsAsync();

      final debtorItems =
          debtItems.where((item) => item.debtorId == debtor.id).toList();
      final debtorPayments =
          payments.where((payment) => payment.debtorId == debtor.id).toList();

      final remainingDebt = debtor.calculateRemainingDebt(debtItems, payments);
      final totalDebt = debtorItems.fold(0.0, (sum, item) => sum + item.price);
      final totalPaid = debtorPayments.fold(
        0.0,
        (sum, payment) => sum + payment.amount,
      );

      final qrData = {
        'version': _version,
        'type': 'debtor_stats',
        'timestamp': DateTime.now().toIso8601String(),
        'debtor': {
          'id': debtor.id,
          'name': debtor.name,
          'notes': debtor.notes,
          'createdAt': debtor.createdAt.toIso8601String(),
          'dueDate': debtor.dueDate?.toIso8601String(),
          'lastActivity': debtor.lastActivity.toIso8601String(),
        },
        'statistics': {
          'totalDebt': totalDebt,
          'totalPaid': totalPaid,
          'remainingDebt': remainingDebt,
          'itemsCount': debtorItems.length,
          'paymentsCount': debtorPayments.length,
          'isOverdue': debtor.checkIfOverdue(),
          'daysOverdue': debtor.getDaysOverdue(),
          'paymentProgress': totalDebt > 0 ? (totalPaid / totalDebt * 100) : 0,
        },
        'details':
            includeDetails
                ? {
                  'items':
                      debtorItems
                          .map(
                            (item) => {
                              'description': item.description,
                              'price': item.price,
                              'date': item.date.toIso8601String(),
                            },
                          )
                          .toList(),
                  'payments':
                      includePaymentHistory
                          ? debtorPayments
                              .map(
                                (payment) => {
                                  'amount': payment.amount,
                                  'date': payment.date.toIso8601String(),
                                  'method': payment.method.toString(),
                                  'notes': payment.notes,
                                },
                              )
                              .toList()
                          : null,
                }
                : null,
      };

      final jsonString = jsonEncode(qrData);
      return '$_qrPrefix$jsonString';
    } catch (e) {
      throw Exception('خطأ في إنشاء QR للمدين: $e');
    }
  }

  /// إنشاء QR لملخص عام للديون
  static Future<String> generateSummaryQR() async {
    try {
      final debtors = await DatabaseService.getAllDebtorsAsync();
      final debtItems = await DatabaseService.getAllDebtItemsAsync();
      final payments = await DatabaseService.getAllPaymentsAsync();

      double totalOutstanding = 0;
      double totalPaid = 0;
      int overdueCount = 0;

      for (final debtor in debtors) {
        final remaining = debtor.calculateRemainingDebt(debtItems, payments);
        totalOutstanding += remaining;

        final debtorPayments = payments.where((p) => p.debtorId == debtor.id);
        totalPaid += debtorPayments.fold(0.0, (sum, p) => sum + p.amount);

        if (debtor.checkIfOverdue()) overdueCount++;
      }

      final qrData = {
        'version': _version,
        'type': 'summary_stats',
        'timestamp': DateTime.now().toIso8601String(),
        'summary': {
          'totalDebtors': debtors.length,
          'totalOutstanding': totalOutstanding,
          'totalPaid': totalPaid,
          'overdueDebtors': overdueCount,
          'totalItems': debtItems.length,
          'totalPayments': payments.length,
          'generatedBy': 'كوزمتك وكماليات باسم',
        },
      };

      final jsonString = jsonEncode(qrData);
      return '$_qrPrefix$jsonString';
    } catch (e) {
      throw Exception('خطأ في إنشاء QR للملخص: $e');
    }
  }

  /// إنشاء صورة QR مع تصميم مخصص
  static Future<Uint8List> generateQRImage({
    required String data,
    double size = 300,
    Color foregroundColor = Colors.black,
    Color backgroundColor = Colors.white,
    String? logoPath,
    String? title,
    String? subtitle,
  }) async {
    try {
      final qrValidationResult = QrValidator.validate(
        data: data,
        version: QrVersions.auto,
        errorCorrectionLevel: QrErrorCorrectLevel.M,
      );

      if (qrValidationResult.status != QrValidationStatus.valid) {
        throw Exception('بيانات QR غير صالحة');
      }

      final painter = QrPainter(
        data: data,
        version: QrVersions.auto,
        errorCorrectionLevel: QrErrorCorrectLevel.M,
        eyeStyle: QrEyeStyle(
          eyeShape: QrEyeShape.square,
          color: foregroundColor,
        ),
        dataModuleStyle: QrDataModuleStyle(
          dataModuleShape: QrDataModuleShape.square,
          color: foregroundColor,
        ),
        gapless: true,
      );

      // إنشاء الصورة
      final picSize = size.toInt();
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      // رسم الخلفية
      final backgroundPaint = Paint()..color = backgroundColor;
      canvas.drawRect(Rect.fromLTWH(0, 0, size, size), backgroundPaint);

      // رسم QR
      painter.paint(canvas, Size(size, size));

      final picture = recorder.endRecording();
      final img = await picture.toImage(picSize, picSize);
      final byteData = await img.toByteData(format: ui.ImageByteFormat.png);

      return byteData!.buffer.asUint8List();
    } catch (e) {
      throw Exception('خطأ في إنشاء صورة QR: $e');
    }
  }

  /// إنشاء QR مع تصميم متقدم يتضمن معلومات إضافية
  static Future<Uint8List> generateAdvancedQRCard({
    required String data,
    required String title,
    String? subtitle,
    String? description,
    Color primaryColor = const Color(0xFF9C27B0),
    double cardWidth = 400,
    double cardHeight = 500,
  }) async {
    try {
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      // رسم الخلفية المتدرجة
      final gradient = ui.Gradient.linear(
        const Offset(0, 0),
        Offset(0, cardHeight),
        [primaryColor.withOpacity(0.1), Colors.white],
      );

      final backgroundPaint = Paint()..shader = gradient;
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(0, 0, cardWidth, cardHeight),
          const Radius.circular(20),
        ),
        backgroundPaint,
      );

      // رسم الحدود
      final borderPaint =
          Paint()
            ..color = primaryColor.withOpacity(0.3)
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2;
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(0, 0, cardWidth, cardHeight),
          const Radius.circular(20),
        ),
        borderPaint,
      );

      // رسم العنوان
      final titlePainter = TextPainter(
        text: TextSpan(
          text: title,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: primaryColor,
          ),
        ),
        textDirection: TextDirection.rtl,
      );
      titlePainter.layout(maxWidth: cardWidth - 40);
      titlePainter.paint(canvas, const Offset(20, 30));

      // رسم العنوان الفرعي
      if (subtitle != null) {
        final subtitlePainter = TextPainter(
          text: TextSpan(
            text: subtitle,
            style: const TextStyle(fontSize: 16, color: Colors.grey),
          ),
          textDirection: TextDirection.rtl,
        );
        subtitlePainter.layout(maxWidth: cardWidth - 40);
        subtitlePainter.paint(canvas, const Offset(20, 70));
      }

      // رسم QR في المنتصف
      final qrSize = 200.0;
      final qrOffset = Offset(
        (cardWidth - qrSize) / 2,
        (cardHeight - qrSize) / 2 - 20,
      );

      final qrPainter = QrPainter(
        data: data,
        version: QrVersions.auto,
        errorCorrectionLevel: QrErrorCorrectLevel.M,
        eyeStyle: const QrEyeStyle(
          eyeShape: QrEyeShape.square,
          color: Colors.black,
        ),
        dataModuleStyle: const QrDataModuleStyle(
          dataModuleShape: QrDataModuleShape.square,
          color: Colors.black,
        ),
        gapless: true,
      );

      canvas.save();
      canvas.translate(qrOffset.dx, qrOffset.dy);
      qrPainter.paint(canvas, Size(qrSize, qrSize));
      canvas.restore();

      // رسم الوصف في الأسفل
      if (description != null) {
        final descPainter = TextPainter(
          text: TextSpan(
            text: description,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          textDirection: TextDirection.rtl,
          textAlign: TextAlign.center,
        );
        descPainter.layout(maxWidth: cardWidth - 40);
        descPainter.paint(canvas, Offset(20, cardHeight - 60));
      }

      // رسم شعار التطبيق
      final logoPainter = TextPainter(
        text: const TextSpan(
          text: 'كوزمتك وكماليات باسم',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey,
          ),
        ),
        textDirection: TextDirection.rtl,
        textAlign: TextAlign.center,
      );
      logoPainter.layout(maxWidth: cardWidth - 40);
      logoPainter.paint(
        canvas,
        Offset((cardWidth - logoPainter.width) / 2, cardHeight - 30),
      );

      final picture = recorder.endRecording();
      final img = await picture.toImage(cardWidth.toInt(), cardHeight.toInt());
      final byteData = await img.toByteData(format: ui.ImageByteFormat.png);

      return byteData!.buffer.asUint8List();
    } catch (e) {
      throw Exception('خطأ في إنشاء بطاقة QR المتقدمة: $e');
    }
  }

  /// مشاركة QR كصورة
  static Future<void> shareQRImage({
    required Uint8List imageBytes,
    required String fileName,
    String? text,
  }) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/$fileName.png');
      await file.writeAsBytes(imageBytes);

      await Share.shareXFiles([
        XFile(file.path),
      ], text: text ?? 'مشاركة من تطبيق كوزمتك وكماليات باسم');
    } catch (e) {
      throw Exception('خطأ في مشاركة QR: $e');
    }
  }

  /// فك تشفير بيانات QR
  static Map<String, dynamic>? decodeQRData(String qrData) {
    try {
      if (!qrData.startsWith(_qrPrefix)) {
        return null; // ليس QR من التطبيق
      }

      final jsonString = qrData.substring(_qrPrefix.length);
      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      // التحقق من الإصدار
      if (data['version'] != _version) {
        throw Exception('إصدار QR غير متوافق');
      }

      return data;
    } catch (e) {
      return null;
    }
  }

  /// التحقق من صحة QR
  static bool isValidDebtorTrackerQR(String qrData) {
    final decoded = decodeQRData(qrData);
    return decoded != null &&
        decoded.containsKey('type') &&
        (decoded['type'] == 'debtor_stats' ||
            decoded['type'] == 'summary_stats');
  }

  /// استخراج معلومات المدين من QR
  static DebtorQRInfo? extractDebtorInfo(String qrData) {
    final decoded = decodeQRData(qrData);
    if (decoded == null || decoded['type'] != 'debtor_stats') {
      return null;
    }

    try {
      return DebtorQRInfo.fromJson(decoded);
    } catch (e) {
      return null;
    }
  }

  /// استخراج ملخص الإحصائيات من QR
  static SummaryQRInfo? extractSummaryInfo(String qrData) {
    final decoded = decodeQRData(qrData);
    if (decoded == null || decoded['type'] != 'summary_stats') {
      return null;
    }

    try {
      return SummaryQRInfo.fromJson(decoded);
    } catch (e) {
      return null;
    }
  }
}

/// معلومات المدين المستخرجة من QR
class DebtorQRInfo {
  final String id;
  final String name;
  final String? phone;
  final String? address;
  final String? notes;
  final DateTime createdAt;
  final DateTime? dueDate;
  final DateTime lastActivity;
  final double totalDebt;
  final double totalPaid;
  final double remainingDebt;
  final int itemsCount;
  final int paymentsCount;
  final bool isOverdue;
  final int daysOverdue;
  final double paymentProgress;
  final DateTime timestamp;

  DebtorQRInfo({
    required this.id,
    required this.name,
    this.phone,
    this.address,
    this.notes,
    required this.createdAt,
    this.dueDate,
    required this.lastActivity,
    required this.totalDebt,
    required this.totalPaid,
    required this.remainingDebt,
    required this.itemsCount,
    required this.paymentsCount,
    required this.isOverdue,
    required this.daysOverdue,
    required this.paymentProgress,
    required this.timestamp,
  });

  factory DebtorQRInfo.fromJson(Map<String, dynamic> json) {
    final debtor = json['debtor'] as Map<String, dynamic>;
    final stats = json['statistics'] as Map<String, dynamic>;

    return DebtorQRInfo(
      id: debtor['id'],
      name: debtor['name'],
      phone: debtor['phone'],
      address: debtor['address'],
      notes: debtor['notes'],
      createdAt: DateTime.parse(debtor['createdAt']),
      dueDate:
          debtor['dueDate'] != null ? DateTime.parse(debtor['dueDate']) : null,
      lastActivity: DateTime.parse(debtor['lastActivity']),
      totalDebt: stats['totalDebt'].toDouble(),
      totalPaid: stats['totalPaid'].toDouble(),
      remainingDebt: stats['remainingDebt'].toDouble(),
      itemsCount: stats['itemsCount'],
      paymentsCount: stats['paymentsCount'],
      isOverdue: stats['isOverdue'],
      daysOverdue: stats['daysOverdue'],
      paymentProgress: stats['paymentProgress'].toDouble(),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

/// ملخص الإحصائيات المستخرج من QR
class SummaryQRInfo {
  final int totalDebtors;
  final double totalOutstanding;
  final double totalPaid;
  final int overdueDebtors;
  final int totalItems;
  final int totalPayments;
  final String generatedBy;
  final DateTime timestamp;

  SummaryQRInfo({
    required this.totalDebtors,
    required this.totalOutstanding,
    required this.totalPaid,
    required this.overdueDebtors,
    required this.totalItems,
    required this.totalPayments,
    required this.generatedBy,
    required this.timestamp,
  });

  factory SummaryQRInfo.fromJson(Map<String, dynamic> json) {
    final summary = json['summary'] as Map<String, dynamic>;

    return SummaryQRInfo(
      totalDebtors: summary['totalDebtors'],
      totalOutstanding: summary['totalOutstanding'].toDouble(),
      totalPaid: summary['totalPaid'].toDouble(),
      overdueDebtors: summary['overdueDebtors'],
      totalItems: summary['totalItems'],
      totalPayments: summary['totalPayments'],
      generatedBy: summary['generatedBy'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}
