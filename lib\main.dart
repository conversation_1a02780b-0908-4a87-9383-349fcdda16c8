import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'services/database_service.dart';
import 'services/notification_service.dart';
import 'services/security_service.dart';
import 'services/encryption_service.dart';
import 'services/audit_service.dart';
import 'services/smart_notification_service.dart';
import 'utils/ui_enhancement_service.dart';
import 'utils/ui_improvements.dart';
import 'providers/debtor_provider.dart';
import 'providers/settings_provider.dart';
import 'screens/main_screen.dart';
import 'screens/add_debtor_screen.dart';
import 'screens/splash_screen.dart';
import 'utils/app_theme.dart';
import 'l10n/app_localizations_delegate.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize database
  await DatabaseService.init();

  // Initialize security services
  await EncryptionService.initialize();
  await SecurityService.initialize();
  await AuditService.initialize();

  // Initialize notifications
  await NotificationService.initialize();

  // Request notification permissions
  await NotificationService.requestPermissions();

  // Initialize smart notifications
  await SmartNotificationService.initialize();

  // Initialize UI enhancements
  await UIEnhancementService.initialize();

  // Set up notification listeners (simplified)
  NotificationService.setListeners(
    onNotificationCreated: (message) {
      debugPrint('Notification created: $message');
    },
    onNotificationDisplayed: (message) {
      debugPrint('Notification displayed: $message');
    },
    onActionReceived: (message) {
      debugPrint('Notification action received: $message');
    },
    onNotificationDismissed: (message) {
      debugPrint('Notification dismissed: $message');
    },
  );

  runApp(const DebtorTrackerApp());
}

class DebtorTrackerApp extends StatelessWidget {
  const DebtorTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => SettingsProvider()..init()),
        ChangeNotifierProvider(
          create: (context) => DebtorProvider()..loadData(),
        ),
      ],
      child: Consumer<SettingsProvider>(
        builder: (context, settings, child) {
          return MaterialApp(
            title:
                settings.language == AppLanguage.arabic
                    ? 'كوزمتك وكماليات باسم'
                    : 'Basim Cosmetics & Accessories',
            debugShowCheckedModeBanner: false,
            theme: UIImprovements.getEnhancedTheme(isDark: false),
            darkTheme: UIImprovements.getEnhancedTheme(isDark: true),
            themeMode: settings.materialThemeMode,
            locale: settings.locale,
            supportedLocales: const [Locale('ar', 'SA'), Locale('en', 'US')],
            localizationsDelegates: [
              const AppLocalizationsDelegate(),
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],

            // RTL support
            builder: (context, child) {
              return Directionality(
                textDirection: settings.textDirection,
                child: child!,
              );
            },

            home: const SplashScreen(),

            // Routes for navigation
            routes: {
              '/main': (context) => const MainScreen(),
              '/add_debtor': (context) => const AddDebtorScreen(),
            },

            onGenerateRoute: (settings) {
              // Handle parameterized routes
              if (settings.name?.startsWith('/debtor-detail/') == true) {
                // In a real app, you'd fetch the debtor by ID and navigate
                // For now, we'll handle this in the navigation calls
                return MaterialPageRoute(
                  builder: (context) => const MainScreen(),
                );
              }
              return null;
            },
          );
        },
      ),
    );
  }
}
