import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/debtor.dart';
import '../models/debt_item.dart';
import '../models/payment.dart';
import '../services/database_service.dart';

import '../services/sound_service.dart';

class SmartNotificationService {
  static const String _lastCheckKey = 'smart_notifications_last_check';
  static const String _notificationHistoryKey = 'notification_history';
  static const String _settingsKey = 'smart_notification_settings';

  static Timer? _periodicTimer;
  static bool _isInitialized = false;

  // إعدادات الإشعارات الذكية
  static const Map<String, dynamic> _defaultSettings = {
    'enabled': true,
    'check_interval_hours': 6, // فحص كل 6 ساعات
    'overdue_notifications': true,
    'due_soon_notifications': true,
    'payment_reminders': true,
    'weekly_summary': true,
    'urgent_only_mode': false,
    'quiet_hours_start': 22, // 10 PM
    'quiet_hours_end': 8, // 8 AM
    'max_notifications_per_day': 5,
    'escalation_enabled': true, // تصعيد الإشعارات للديون المتأخرة
  };

  // تهيئة الخدمة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadSettings();
    await _startPeriodicCheck();
    _isInitialized = true;

    debugPrint('Smart Notification Service initialized');
  }

  // بدء الفحص الدوري
  static Future<void> _startPeriodicCheck() async {
    final settings = await getSettings();
    if (!settings['enabled']) return;

    final intervalHours = settings['check_interval_hours'] as int;
    _periodicTimer?.cancel();

    _periodicTimer = Timer.periodic(
      Duration(hours: intervalHours),
      (timer) => _performSmartCheck(),
    );

    // فحص فوري عند البدء
    await _performSmartCheck();
  }

  // إيقاف الخدمة
  static void dispose() {
    _periodicTimer?.cancel();
    _isInitialized = false;
  }

  // الفحص الذكي الرئيسي
  static Future<void> _performSmartCheck() async {
    try {
      final settings = await getSettings();
      if (!settings['enabled']) return;

      // التحقق من الساعات الهادئة
      if (_isQuietHours(settings)) return;

      // التحقق من حد الإشعارات اليومية
      if (await _hasReachedDailyLimit(settings)) return;

      final debtors = await DatabaseService.getAllDebtorsAsync();
      final debtItems = await DatabaseService.getAllDebtItemsAsync();
      final payments = await DatabaseService.getAllPaymentsAsync();

      // تحليل البيانات وإرسال الإشعارات المناسبة
      await _analyzeAndNotify(debtors, debtItems, payments, settings);

      await _updateLastCheckTime();
    } catch (e) {
      debugPrint('Error in smart notification check: $e');
    }
  }

  // تحليل البيانات وإرسال الإشعارات
  static Future<void> _analyzeAndNotify(
    List<Debtor> debtors,
    List<DebtItem> debtItems,
    List<Payment> payments,
    Map<String, dynamic> settings,
  ) async {
    final now = DateTime.now();
    final notifications = <Map<String, dynamic>>[];

    for (final debtor in debtors) {
      final remainingDebt = debtor.calculateRemainingDebt(debtItems, payments);

      // تخطي المديونين الذين سددوا بالكامل
      if (remainingDebt <= 0) continue;

      // فحص التأخير في الدفع
      if (settings['overdue_notifications'] && debtor.dueDate != null) {
        final daysOverdue = debtor.getDaysOverdue();
        if (daysOverdue > 0) {
          final priority = _calculateOverduePriority(
            daysOverdue,
            remainingDebt,
          );
          notifications.add({
            'type': 'overdue',
            'debtor': debtor,
            'priority': priority,
            'days_overdue': daysOverdue,
            'amount': remainingDebt,
          });
        }
      }

      // فحص الديون المستحقة قريباً
      if (settings['due_soon_notifications'] && debtor.dueDate != null) {
        final daysToDue = debtor.dueDate!.difference(now).inDays;
        if (daysToDue > 0 && daysToDue <= 3) {
          notifications.add({
            'type': 'due_soon',
            'debtor': debtor,
            'priority': _calculateDueSoonPriority(daysToDue, remainingDebt),
            'days_to_due': daysToDue,
            'amount': remainingDebt,
          });
        }
      }

      // فحص الديون الطويلة بدون نشاط
      if (settings['payment_reminders']) {
        final daysSinceLastActivity =
            now.difference(debtor.lastActivity).inDays;
        if (daysSinceLastActivity >= 7 && remainingDebt > 0) {
          notifications.add({
            'type': 'inactive',
            'debtor': debtor,
            'priority': _calculateInactivePriority(
              daysSinceLastActivity,
              remainingDebt,
            ),
            'days_inactive': daysSinceLastActivity,
            'amount': remainingDebt,
          });
        }
      }
    }

    // ترتيب الإشعارات حسب الأولوية
    notifications.sort(
      (a, b) => (b['priority'] as int).compareTo(a['priority'] as int),
    );

    // إرسال الإشعارات عالية الأولوية
    await _sendPriorityNotifications(notifications, settings);
  }

  // حساب أولوية الديون المتأخرة
  static int _calculateOverduePriority(int daysOverdue, double amount) {
    int priority = 50; // أولوية أساسية

    // زيادة الأولوية حسب عدد الأيام
    if (daysOverdue >= 30) {
      priority += 40;
    } else if (daysOverdue >= 14) {
      priority += 30;
    } else if (daysOverdue >= 7) {
      priority += 20;
    } else {
      priority += 10;
    }

    // زيادة الأولوية حسب المبلغ
    if (amount >= 1000000) {
      priority += 30; // مليون فأكثر
    } else if (amount >= 500000) {
      priority += 20; // نصف مليون فأكثر
    } else if (amount >= 100000) {
      priority += 10; // مئة ألف فأكثر
    }

    return priority;
  }

  // حساب أولوية الديون المستحقة قريباً
  static int _calculateDueSoonPriority(int daysToDue, double amount) {
    int priority = 30; // أولوية أساسية أقل من المتأخرة

    // زيادة الأولوية كلما قل الوقت
    if (daysToDue == 1)
      priority += 25;
    else if (daysToDue == 2)
      priority += 15;
    else if (daysToDue == 3)
      priority += 10;

    // زيادة الأولوية حسب المبلغ
    if (amount >= 500000)
      priority += 20;
    else if (amount >= 100000)
      priority += 10;

    return priority;
  }

  // حساب أولوية الديون غير النشطة
  static int _calculateInactivePriority(int daysInactive, double amount) {
    int priority = 20; // أولوية أساسية منخفضة

    // زيادة الأولوية حسب فترة عدم النشاط
    if (daysInactive >= 30)
      priority += 20;
    else if (daysInactive >= 14)
      priority += 15;
    else if (daysInactive >= 7)
      priority += 10;

    // زيادة الأولوية حسب المبلغ
    if (amount >= 500000)
      priority += 15;
    else if (amount >= 100000)
      priority += 10;

    return priority;
  }

  // إرسال الإشعارات عالية الأولوية
  static Future<void> _sendPriorityNotifications(
    List<Map<String, dynamic>> notifications,
    Map<String, dynamic> settings,
  ) async {
    final maxNotifications = settings['max_notifications_per_day'] as int;
    final urgentOnly = settings['urgent_only_mode'] as bool;

    int sentCount = 0;

    for (final notification in notifications) {
      if (sentCount >= maxNotifications) break;

      final priority = notification['priority'] as int;

      // في الوضع العاجل، إرسال الإشعارات عالية الأولوية فقط
      if (urgentOnly && priority < 70) continue;

      await _sendSmartNotification(notification, settings);
      await _recordNotificationSent(notification);

      sentCount++;

      // تأخير قصير بين الإشعارات
      await Future.delayed(const Duration(seconds: 2));
    }
  }

  // إرسال إشعار ذكي
  static Future<void> _sendSmartNotification(
    Map<String, dynamic> notification,
    Map<String, dynamic> settings,
  ) async {
    final debtor = notification['debtor'] as Debtor;
    final type = notification['type'] as String;
    final amount = notification['amount'] as double;

    String title, body;
    Color notificationColor;
    String soundType;

    switch (type) {
      case 'overdue':
        final daysOverdue = notification['days_overdue'] as int;
        title = '⚠️ دين متأخر - ${debtor.name}';
        body = 'متأخر $daysOverdue يوم • ${amount.toStringAsFixed(0)} د.ع';
        notificationColor = Colors.red;
        soundType = 'urgent';
        break;

      case 'due_soon':
        final daysToDue = notification['days_to_due'] as int;
        title = '⏰ دين مستحق قريباً - ${debtor.name}';
        body = 'مستحق خلال $daysToDue أيام • ${amount.toStringAsFixed(0)} د.ع';
        notificationColor = Colors.orange;
        soundType = 'warning';
        break;

      case 'inactive':
        final daysInactive = notification['days_inactive'] as int;
        title = '💤 دين بدون نشاط - ${debtor.name}';
        body =
            'لا يوجد نشاط منذ $daysInactive يوم • ${amount.toStringAsFixed(0)} د.ع';
        notificationColor = Colors.blue;
        soundType = 'reminder';
        break;

      default:
        return;
    }

    // إرسال الإشعار المحلي
    await _sendLocalNotification(
      title: title,
      body: body,
      payload: 'smart_notification:${debtor.id}:$type',
      color: notificationColor,
      priority: notification['priority'] as int,
    );

    // تشغيل الصوت المناسب
    SoundService.playNotificationSound(soundType);
  }

  // إرسال إشعار محلي
  static Future<void> _sendLocalNotification({
    required String title,
    required String body,
    required String payload,
    required Color color,
    required int priority,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      'smart_debt_notifications',
      'إشعارات ذكية للديون',
      channelDescription: 'إشعارات ذكية ومتقدمة لمتابعة حالة المديونين',
      importance: priority >= 70 ? Importance.max : Importance.high,
      priority: priority >= 70 ? Priority.max : Priority.high,
      icon: '@mipmap/basim',
      color: color,
      enableVibration: true,
      playSound: true,
      autoCancel: true,
      styleInformation: BigTextStyleInformation(
        body,
        contentTitle: title,
        summaryText: 'نظام الإشعارات الذكي',
      ),
    );

    final details = NotificationDetails(android: androidDetails);

    final FlutterLocalNotificationsPlugin notifications =
        FlutterLocalNotificationsPlugin();
    await notifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      details,
      payload: payload,
    );
  }

  // التحقق من الساعات الهادئة
  static bool _isQuietHours(Map<String, dynamic> settings) {
    final now = DateTime.now();
    final currentHour = now.hour;
    final quietStart = settings['quiet_hours_start'] as int;
    final quietEnd = settings['quiet_hours_end'] as int;

    if (quietStart < quietEnd) {
      return currentHour >= quietStart && currentHour < quietEnd;
    } else {
      return currentHour >= quietStart || currentHour < quietEnd;
    }
  }

  // التحقق من الوصول للحد اليومي
  static Future<bool> _hasReachedDailyLimit(
    Map<String, dynamic> settings,
  ) async {
    final maxDaily = settings['max_notifications_per_day'] as int;
    final today = DateTime.now();
    final todayKey = '${today.year}-${today.month}-${today.day}';

    final prefs = await SharedPreferences.getInstance();
    final todayCount = prefs.getInt('notifications_count_$todayKey') ?? 0;

    return todayCount >= maxDaily;
  }

  // تسجيل إرسال إشعار
  static Future<void> _recordNotificationSent(
    Map<String, dynamic> notification,
  ) async {
    final today = DateTime.now();
    final todayKey = '${today.year}-${today.month}-${today.day}';

    final prefs = await SharedPreferences.getInstance();
    final todayCount = prefs.getInt('notifications_count_$todayKey') ?? 0;
    await prefs.setInt('notifications_count_$todayKey', todayCount + 1);

    // حفظ تاريخ الإشعار
    final history = prefs.getStringList(_notificationHistoryKey) ?? [];
    final record =
        '${today.toIso8601String()}:${notification['type']}:${(notification['debtor'] as Debtor).id}';
    history.add(record);

    // الاحتفاظ بآخر 100 إشعار فقط
    if (history.length > 100) {
      history.removeRange(0, history.length - 100);
    }

    await prefs.setStringList(_notificationHistoryKey, history);
  }

  // تحديث وقت آخر فحص
  static Future<void> _updateLastCheckTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastCheckKey, DateTime.now().toIso8601String());
  }

  // تحميل الإعدادات
  static Future<void> _loadSettings() async {
    // تحميل الإعدادات من SharedPreferences
    await getSettings();
  }

  // الحصول على الإعدادات
  static Future<Map<String, dynamic>> getSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final settings = Map<String, dynamic>.from(_defaultSettings);

    // تحميل الإعدادات المحفوظة
    settings['enabled'] =
        prefs.getBool('smart_notifications_enabled') ??
        _defaultSettings['enabled'];
    settings['check_interval_hours'] =
        prefs.getInt('smart_notifications_interval') ??
        _defaultSettings['check_interval_hours'];
    settings['overdue_notifications'] =
        prefs.getBool('smart_overdue_notifications') ??
        _defaultSettings['overdue_notifications'];
    settings['due_soon_notifications'] =
        prefs.getBool('smart_due_soon_notifications') ??
        _defaultSettings['due_soon_notifications'];
    settings['payment_reminders'] =
        prefs.getBool('smart_payment_reminders') ??
        _defaultSettings['payment_reminders'];
    settings['weekly_summary'] =
        prefs.getBool('smart_weekly_summary') ??
        _defaultSettings['weekly_summary'];
    settings['urgent_only_mode'] =
        prefs.getBool('smart_urgent_only_mode') ??
        _defaultSettings['urgent_only_mode'];
    settings['quiet_hours_start'] =
        prefs.getInt('smart_quiet_hours_start') ??
        _defaultSettings['quiet_hours_start'];
    settings['quiet_hours_end'] =
        prefs.getInt('smart_quiet_hours_end') ??
        _defaultSettings['quiet_hours_end'];
    settings['max_notifications_per_day'] =
        prefs.getInt('smart_max_notifications_per_day') ??
        _defaultSettings['max_notifications_per_day'];
    settings['escalation_enabled'] =
        prefs.getBool('smart_escalation_enabled') ??
        _defaultSettings['escalation_enabled'];

    return settings;
  }

  // حفظ الإعدادات
  static Future<void> saveSettings(Map<String, dynamic> settings) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setBool('smart_notifications_enabled', settings['enabled']);
    await prefs.setInt(
      'smart_notifications_interval',
      settings['check_interval_hours'],
    );
    await prefs.setBool(
      'smart_overdue_notifications',
      settings['overdue_notifications'],
    );
    await prefs.setBool(
      'smart_due_soon_notifications',
      settings['due_soon_notifications'],
    );
    await prefs.setBool(
      'smart_payment_reminders',
      settings['payment_reminders'],
    );
    await prefs.setBool('smart_weekly_summary', settings['weekly_summary']);
    await prefs.setBool('smart_urgent_only_mode', settings['urgent_only_mode']);
    await prefs.setInt(
      'smart_quiet_hours_start',
      settings['quiet_hours_start'],
    );
    await prefs.setInt('smart_quiet_hours_end', settings['quiet_hours_end']);
    await prefs.setInt(
      'smart_max_notifications_per_day',
      settings['max_notifications_per_day'],
    );
    await prefs.setBool(
      'smart_escalation_enabled',
      settings['escalation_enabled'],
    );

    // إعادة تشغيل الخدمة بالإعدادات الجديدة
    if (_isInitialized) {
      await _startPeriodicCheck();
    }
  }

  // إرسال ملخص أسبوعي
  static Future<void> sendWeeklySummary() async {
    try {
      final settings = await getSettings();
      if (!settings['weekly_summary']) return;

      final debtors = await DatabaseService.getAllDebtorsAsync();
      final debtItems = await DatabaseService.getAllDebtItemsAsync();
      final payments = await DatabaseService.getAllPaymentsAsync();

      final summary = await _generateWeeklySummary(
        debtors,
        debtItems,
        payments,
      );

      await _sendLocalNotification(
        title: '📊 الملخص الأسبوعي للديون',
        body: summary,
        payload: 'weekly_summary',
        color: Colors.blue,
        priority: 60,
      );
    } catch (e) {
      debugPrint('Error sending weekly summary: $e');
    }
  }

  // إنشاء ملخص أسبوعي
  static Future<String> _generateWeeklySummary(
    List<Debtor> debtors,
    List<DebtItem> debtItems,
    List<Payment> payments,
  ) async {
    final now = DateTime.now();
    final weekAgo = now.subtract(const Duration(days: 7));

    // حساب الإحصائيات
    final totalDebtors = debtors.length;
    final overdueDebtors = debtors.where((d) => d.checkIfOverdue()).length;

    double totalOutstanding = 0;
    double weeklyPayments = 0;
    double weeklyNewDebt = 0;

    for (final debtor in debtors) {
      totalOutstanding += debtor.calculateRemainingDebt(debtItems, payments);
    }

    // المدفوعات الأسبوعية
    final weeklyPaymentsList =
        payments.where((p) => p.date.isAfter(weekAgo)).toList();
    weeklyPayments = weeklyPaymentsList.fold(0.0, (sum, p) => sum + p.amount);

    // الديون الجديدة الأسبوعية
    final weeklyDebtItems =
        debtItems.where((item) => item.date.isAfter(weekAgo)).toList();
    weeklyNewDebt = weeklyDebtItems.fold(0.0, (sum, item) => sum + item.price);

    return '''
📈 إجمالي المديونين: $totalDebtors
⚠️ متأخرين: $overdueDebtors
💰 إجمالي المبالغ المستحقة: ${totalOutstanding.toStringAsFixed(0)} د.ع
💵 مدفوعات هذا الأسبوع: ${weeklyPayments.toStringAsFixed(0)} د.ع
📊 ديون جديدة: ${weeklyNewDebt.toStringAsFixed(0)} د.ع
''';
  }

  // فحص فوري للديون المتأخرة
  static Future<void> checkOverdueDebtsNow() async {
    await _performSmartCheck();
  }

  // إرسال تذكير عاجل لمدين محدد
  static Future<void> sendUrgentReminder(String debtorId) async {
    try {
      final debtor = DatabaseService.getDebtor(debtorId);
      if (debtor == null) return;

      final debtItems = await DatabaseService.getAllDebtItemsAsync();
      final payments = await DatabaseService.getAllPaymentsAsync();
      final remainingDebt = debtor.calculateRemainingDebt(debtItems, payments);

      if (remainingDebt <= 0) return;

      final daysOverdue = debtor.getDaysOverdue();

      await _sendLocalNotification(
        title: '🚨 تذكير عاجل - ${debtor.name}',
        body:
            daysOverdue > 0
                ? 'متأخر $daysOverdue يوم • ${remainingDebt.toStringAsFixed(0)} د.ع'
                : 'مبلغ مستحق: ${remainingDebt.toStringAsFixed(0)} د.ع',
        payload: 'urgent_reminder:$debtorId',
        color: Colors.red,
        priority: 100,
      );

      SoundService.playUrgentReminderSound();
    } catch (e) {
      debugPrint('Error sending urgent reminder: $e');
    }
  }

  // الحصول على إحصائيات الإشعارات
  static Future<Map<String, dynamic>> getNotificationStats() async {
    final prefs = await SharedPreferences.getInstance();
    final history = prefs.getStringList(_notificationHistoryKey) ?? [];

    final today = DateTime.now();
    final todayKey = '${today.year}-${today.month}-${today.day}';
    final todayCount = prefs.getInt('notifications_count_$todayKey') ?? 0;

    // تحليل التاريخ
    final last7Days = <String, int>{};
    for (int i = 0; i < 7; i++) {
      final date = today.subtract(Duration(days: i));
      final dateKey = '${date.year}-${date.month}-${date.day}';
      last7Days[dateKey] = prefs.getInt('notifications_count_$dateKey') ?? 0;
    }

    return {
      'total_sent': history.length,
      'today_count': todayCount,
      'last_7_days': last7Days,
      'last_check': prefs.getString(_lastCheckKey),
    };
  }

  // مسح تاريخ الإشعارات
  static Future<void> clearNotificationHistory() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_notificationHistoryKey);

    // مسح عدادات الأيام
    final keys = prefs.getKeys().where(
      (key) => key.startsWith('notifications_count_'),
    );
    for (final key in keys) {
      await prefs.remove(key);
    }
  }

  // تفعيل/إلغاء تفعيل الوضع العاجل
  static Future<void> toggleUrgentMode(bool enabled) async {
    final settings = await getSettings();
    settings['urgent_only_mode'] = enabled;
    await saveSettings(settings);
  }

  // جدولة إشعار مخصص
  static Future<void> scheduleCustomNotification({
    required String debtorId,
    required DateTime scheduledTime,
    required String message,
  }) async {
    // يمكن تطوير هذه الوظيفة لاحقاً لجدولة إشعارات مخصصة
    debugPrint(
      'Custom notification scheduled for $debtorId at $scheduledTime: $message',
    );
  }
}
