import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../widgets/common_widgets.dart' as common_widgets;
import '../widgets/overflow_safe_wrapper.dart';
import '../l10n/app_localizations.dart';
import '../utils/app_theme.dart';
import 'about_screen.dart';
import 'backup_sync_screen.dart';
import 'storage_management_screen.dart';
import 'security_settings_screen.dart';
import 'sound_settings_screen.dart';
import 'smart_notifications_settings_screen.dart';
import '../services/enhanced_sharing_service.dart';
import '../services/advanced_cloud_sync_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  Future<void> _showCloudSyncOptions(BuildContext context) async {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'خيارات المزامنة السحابية',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),

                ListTile(
                  leading: const Icon(Icons.cloud, color: Colors.blue),
                  title: const Text('Google Drive'),
                  subtitle: const Text('مزامنة مع حساب Google'),
                  onTap: () async {
                    Navigator.pop(context);
                    await _setupGoogleSync();
                  },
                ),

                ListTile(
                  leading: const Icon(Icons.cloud, color: Colors.grey),
                  title: const Text('iCloud'),
                  subtitle: const Text('مزامنة مع iCloud (iOS فقط)'),
                  onTap: () async {
                    Navigator.pop(context);
                    await _setupiCloudSync();
                  },
                ),

                ListTile(
                  leading: const Icon(Icons.backup),
                  title: const Text('النسخ الاحتياطي المحلي'),
                  subtitle: const Text('إدارة النسخ الاحتياطية المحلية'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const BackupSyncScreen(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }

  Future<void> _setupGoogleSync() async {
    try {
      final bool success = await CloudSyncService.initializeProvider(
        CloudProvider.google,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم ربط Google Drive بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في ربط Google Drive'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _setupiCloudSync() async {
    try {
      final bool success = await CloudSyncService.initializeProvider(
        CloudProvider.icloud,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم ربط iCloud بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في ربط iCloud أو غير متاح'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Scaffold(
      appBar: common_widgets.CustomAppBar(title: l10n.settingsTitle),
      body: Consumer<SettingsProvider>(
        builder: (context, settings, child) {
          return ResponsiveWrapper(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Language & Theme Section
                _buildLanguageThemeSection(context, settings),
                const SizedBox(height: 24),
                // Language Section
                _buildSectionHeader(context, l10n.language, Icons.language),
                _buildLanguageCard(context, settings),

                const SizedBox(height: 24),

                // Notifications Section
                _buildSectionHeader(
                  context,
                  l10n.notifications,
                  Icons.notifications,
                ),
                _buildNotificationCard(context, settings),

                const SizedBox(height: 24),

                // Theme Section
                _buildSectionHeader(context, l10n.appearance, Icons.palette),
                _buildThemeCard(context, settings),

                const SizedBox(height: 24),

                // Data Section
                _buildSectionHeader(
                  context,
                  l10n.dataManagement,
                  Icons.storage,
                ),
                _buildDataCard(context, settings),

                const SizedBox(height: 24),

                // Security Section
                _buildSectionHeader(
                  context,
                  l10n.privacySecurity,
                  Icons.security,
                ),
                _buildSecurityCard(context),

                const SizedBox(height: 24),

                // Sound Section
                _buildSectionHeader(
                  context,
                  l10n.soundSettings,
                  Icons.volume_up,
                ),
                _buildSoundCard(context),

                const SizedBox(height: 24),

                // About Section
                _buildSectionHeader(context, l10n.aboutApp, Icons.info),
                _buildAboutCard(context),

                const SizedBox(height: 32),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: AppTheme.primaryColor, size: 20),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageCard(BuildContext context, SettingsProvider settings) {
    final l10n = AppLocalizations.of(context);
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.language),
            title: Text(l10n.appLanguage),
            subtitle: Text(
              settings.currentLanguage == 'ar' ? l10n.arabic : l10n.english,
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _showLanguageDialog(context, settings),
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.text_fields),
            title: Text(l10n.rtlSupport),
            subtitle: Text(l10n.enableRTL),
            value: settings.isRTL,
            onChanged: (value) => settings.setRTL(value),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(
    BuildContext context,
    SettingsProvider settings,
  ) {
    final l10n = AppLocalizations.of(context);
    return Card(
      child: Column(
        children: [
          SwitchListTile(
            secondary: const Icon(Icons.notifications),
            title: Text(l10n.enableNotifications),
            subtitle: Consumer<SettingsProvider>(
              builder: (context, settings, child) {
                return Text(
                  settings.language == AppLanguage.arabic
                      ? 'استقبال إشعارات التطبيق'
                      : 'Receive app notifications',
                );
              },
            ),
            value: settings.notificationsEnabled,
            onChanged: (value) => settings.setNotificationsEnabled(value),
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.schedule),
            title: Consumer<SettingsProvider>(
              builder: (context, settings, child) {
                return Text(
                  settings.language == AppLanguage.arabic
                      ? 'تذكيرات الديون'
                      : 'Debt Reminders',
                );
              },
            ),
            subtitle: Consumer<SettingsProvider>(
              builder: (context, settings, child) {
                return Text(
                  settings.language == AppLanguage.arabic
                      ? 'إشعارات دورية للديون المستحقة'
                      : 'Periodic notifications for outstanding debts',
                );
              },
            ),
            value: settings.debtRemindersEnabled,
            onChanged:
                settings.notificationsEnabled
                    ? (value) => settings.setDebtRemindersEnabled(value)
                    : null,
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.add_alert),
            title: const Text('إشعارات العمليات'),
            subtitle: const Text('إشعارات عند إضافة عناصر أو مدفوعات'),
            value: settings.operationNotificationsEnabled,
            onChanged:
                settings.notificationsEnabled
                    ? (value) =>
                        settings.setOperationNotificationsEnabled(value)
                    : null,
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.smart_toy),
            title: const Text('الإشعارات الذكية'),
            subtitle: const Text('إعدادات متقدمة للإشعارات الذكية'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap:
                settings.notificationsEnabled
                    ? () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder:
                              (context) =>
                                  const SmartNotificationsSettingsScreen(),
                        ),
                      );
                    }
                    : null,
          ),
        ],
      ),
    );
  }

  Widget _buildThemeCard(BuildContext context, SettingsProvider settings) {
    final l10n = AppLocalizations.of(context);
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.brightness_6),
            title: Text(l10n.themeMode),
            subtitle: Text(_getThemeModeText(settings.themeMode)),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _showThemeDialog(context, settings),
          ),
          const Divider(height: 1),
          SwitchListTile(
            secondary: const Icon(Icons.animation),
            title: Consumer<SettingsProvider>(
              builder: (context, settings, child) {
                return Text(
                  settings.language == AppLanguage.arabic
                      ? 'الرسوم المتحركة'
                      : 'Animations',
                );
              },
            ),
            subtitle: Consumer<SettingsProvider>(
              builder: (context, settings, child) {
                return Text(
                  settings.language == AppLanguage.arabic
                      ? 'تفعيل الانتقالات المتحركة'
                      : 'Enable animated transitions',
                );
              },
            ),
            value: settings.animationsEnabled,
            onChanged: (value) => settings.setAnimationsEnabled(value),
          ),
        ],
      ),
    );
  }

  Widget _buildDataCard(BuildContext context, SettingsProvider settings) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.backup),
            title: const Text('نسخ احتياطي ومزامنة'),
            subtitle: const Text('إدارة النسخ الاحتياطي والمزامنة السحابية'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _showCloudSyncOptions(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.storage_rounded),
            title: const Text('إدارة التخزين'),
            subtitle: const Text('إدارة المساحة وصلاحيات التخزين'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap:
                () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const StorageManagementScreen(),
                  ),
                ),
          ),

          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.restore),
            title: const Text('استعادة البيانات'),
            subtitle: const Text('استعادة من نسخة احتياطية'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _showRestoreDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: Icon(Icons.delete_forever, color: AppTheme.errorColor),
            title: Text(
              'مسح جميع البيانات',
              style: TextStyle(color: AppTheme.errorColor),
            ),
            subtitle: const Text('حذف جميع المدينين والبيانات'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _showClearDataDialog(context),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityCard(BuildContext context) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.security),
            title: const Text('إعدادات الأمان'),
            subtitle: const Text('قفل التطبيق والحماية البيومترية'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap:
                () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const SecuritySettingsScreen(),
                  ),
                ),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.enhanced_encryption),
            title: const Text('تشفير البيانات'),
            subtitle: const Text('حماية البيانات بالتشفير المتقدم'),
            trailing: const Icon(Icons.check_circle, color: Colors.green),
            onTap:
                () => ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('البيانات محمية بالتشفير المتقدم'),
                    backgroundColor: Colors.green,
                  ),
                ),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.history),
            title: const Text('سجل العمليات'),
            subtitle: const Text('تتبع جميع العمليات والتغييرات'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap:
                () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const SecuritySettingsScreen(),
                  ),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildSoundCard(BuildContext context) {
    return Card(
      child: ListTile(
        leading: const Icon(Icons.volume_up),
        title: const Text('إعدادات الأصوات'),
        subtitle: const Text('اختيار نوع الأصوات والإشعارات الصوتية'),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const SoundSettingsScreen(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAboutCard(BuildContext context) {
    return Card(
      child: SingleChildScrollView(
        child: Column(
          children: [
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('حول التطبيق'),
              subtitle: const Text('معلومات التطبيق والمطور'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap:
                  () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const AboutScreen(),
                    ),
                  ),
            ),
            const Divider(height: 1),
            ListTile(
              leading: const Icon(Icons.star_rate),
              title: const Text('تقييم التطبيق'),
              subtitle: const Text('قيم التطبيق في المتجر'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => _showRatingDialog(context),
            ),
            const Divider(height: 1),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('مشاركة التطبيق'),
              subtitle: const Text('شارك التطبيق مع الأصدقاء'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => _shareApp(context),
            ),
          ],
        ),
      ),
    );
  }

  String _getThemeModeText(ThemeMode mode) {
    final l10n = AppLocalizations.of(context);
    switch (mode) {
      case ThemeMode.light:
        return l10n.lightMode;
      case ThemeMode.dark:
        return l10n.darkMode;
      case ThemeMode.system:
        return l10n.systemMode;
    }
  }

  void _showLanguageDialog(BuildContext context, SettingsProvider settings) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر اللغة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('العربية'),
                  value: 'ar',
                  groupValue: settings.currentLanguage,
                  onChanged: (value) {
                    if (value != null) {
                      settings.setLanguage(value);
                      Navigator.of(context).pop();
                    }
                  },
                ),
                RadioListTile<String>(
                  title: const Text('English'),
                  value: 'en',
                  groupValue: settings.currentLanguage,
                  onChanged: (value) {
                    if (value != null) {
                      settings.setLanguage(value);
                      Navigator.of(context).pop();
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  void _showThemeDialog(BuildContext context, SettingsProvider settings) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر المظهر'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<ThemeMode>(
                  title: const Text('فاتح'),
                  value: ThemeMode.light,
                  groupValue: settings.themeMode,
                  onChanged: (value) {
                    if (value != null) {
                      settings.setThemeMode(value);
                      Navigator.of(context).pop();
                    }
                  },
                ),
                RadioListTile<ThemeMode>(
                  title: const Text('داكن'),
                  value: ThemeMode.dark,
                  groupValue: settings.themeMode,
                  onChanged: (value) {
                    if (value != null) {
                      settings.setThemeMode(value);
                      Navigator.of(context).pop();
                    }
                  },
                ),
                RadioListTile<ThemeMode>(
                  title: const Text('تلقائي'),
                  value: ThemeMode.system,
                  groupValue: settings.themeMode,
                  onChanged: (value) {
                    if (value != null) {
                      settings.setThemeMode(value);
                      Navigator.of(context).pop();
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  void _showRestoreDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('استعادة البيانات'),
            content: const Text('هذه الميزة ستكون متاحة في التحديث القادم'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  void _showClearDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تحذير'),
            content: const Text(
              'هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  // TODO: Implement clear data functionality
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('سيتم تنفيذ هذه الميزة قريباً'),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorColor,
                ),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  void _showRatingDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تقييم التطبيق'),
            content: const Text(
              'شكراً لاستخدامك التطبيق! يرجى تقييمه في المتجر.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('لاحقاً'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // TODO: Open app store
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('سيتم فتح المتجر قريباً')),
                  );
                },
                child: const Text('تقييم'),
              ),
            ],
          ),
    );
  }

  void _shareApp(BuildContext context) {
    SharingService.showSharingOptions(
      context: context,
      title: 'مشاركة التطبيق',
      onPlatformSelected: (platform) {
        SharingService.shareApp(context: context, platform: platform);
      },
    );
  }

  Widget _buildLanguageThemeSection(
    BuildContext context,
    SettingsProvider settings,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.palette,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  settings.language == AppLanguage.arabic
                      ? 'المظهر واللغة'
                      : 'Appearance & Language',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Language Setting
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Icon(
                settings.getLanguageIcon(settings.language),
                color: AppTheme.primaryColor,
              ),
              title: Text(
                settings.language == AppLanguage.arabic ? 'اللغة' : 'Language',
              ),
              subtitle: Text(
                settings.getLanguageDisplayName(settings.language),
              ),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => _showAppLanguageDialog(context, settings),
            ),

            const Divider(),

            // Theme Setting
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Icon(
                settings.getThemeModeIcon(settings.appThemeMode),
                color: AppTheme.primaryColor,
              ),
              title: Text(
                settings.language == AppLanguage.arabic ? 'المظهر' : 'Theme',
              ),
              subtitle: Text(
                settings.getThemeModeDisplayName(settings.appThemeMode),
              ),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => _showAppThemeDialog(context, settings),
            ),
          ],
        ),
      ),
    );
  }

  void _showAppLanguageDialog(BuildContext context, SettingsProvider settings) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              settings.language == AppLanguage.arabic
                  ? 'اختر اللغة'
                  : 'Select Language',
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  AppLanguage.values.map((language) {
                    return RadioListTile<AppLanguage>(
                      title: Text(settings.getLanguageDisplayName(language)),
                      value: language,
                      groupValue: settings.language,
                      onChanged: (value) {
                        if (value != null) {
                          settings.setAppLanguage(value);
                          Navigator.pop(context);
                        }
                      },
                    );
                  }).toList(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  settings.language == AppLanguage.arabic ? 'إلغاء' : 'Cancel',
                ),
              ),
            ],
          ),
    );
  }

  void _showAppThemeDialog(BuildContext context, SettingsProvider settings) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              settings.language == AppLanguage.arabic
                  ? 'اختر المظهر'
                  : 'Select Theme',
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  AppThemeMode.values.map((themeMode) {
                    return RadioListTile<AppThemeMode>(
                      title: Row(
                        children: [
                          Icon(settings.getThemeModeIcon(themeMode)),
                          const SizedBox(width: 8),
                          Text(settings.getThemeModeDisplayName(themeMode)),
                        ],
                      ),
                      value: themeMode,
                      groupValue: settings.appThemeMode,
                      onChanged: (value) {
                        if (value != null) {
                          settings.setAppThemeMode(value);
                          Navigator.pop(context);
                        }
                      },
                    );
                  }).toList(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  settings.language == AppLanguage.arabic ? 'إلغاء' : 'Cancel',
                ),
              ),
            ],
          ),
    );
  }
}
