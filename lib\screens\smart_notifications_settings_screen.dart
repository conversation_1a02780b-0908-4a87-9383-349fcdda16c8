import 'package:flutter/material.dart';
import '../services/smart_notification_service.dart';

class SmartNotificationsSettingsScreen extends StatefulWidget {
  const SmartNotificationsSettingsScreen({super.key});

  @override
  State<SmartNotificationsSettingsScreen> createState() =>
      _SmartNotificationsSettingsScreenState();
}

class _SmartNotificationsSettingsScreenState
    extends State<SmartNotificationsSettingsScreen> {
  Map<String, dynamic> _settings = {};
  bool _isLoading = true;
  Map<String, dynamic> _stats = {};

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadStats();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await SmartNotificationService.getSettings();
      setState(() {
        _settings = settings;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الإعدادات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadStats() async {
    try {
      final stats = await SmartNotificationService.getNotificationStats();
      setState(() {
        _stats = stats;
      });
    } catch (e) {
      debugPrint('Error loading stats: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      await SmartNotificationService.saveSettings(_settings);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الإعدادات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الإشعارات الذكية'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _loadSettings();
              _loadStats();
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStatsCard(),
                    const SizedBox(height: 16),
                    _buildMainSettingsCard(),
                    const SizedBox(height: 16),
                    _buildNotificationTypesCard(),
                    const SizedBox(height: 16),
                    _buildTimingSettingsCard(),
                    const SizedBox(height: 16),
                    _buildAdvancedSettingsCard(),
                    const SizedBox(height: 16),
                    _buildActionButtons(),
                  ],
                ),
              ),
    );
  }

  Widget _buildStatsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'إحصائيات الإشعارات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'اليوم',
                    '${_stats['today_count'] ?? 0}',
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الإجمالي',
                    '${_stats['total_sent'] ?? 0}',
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (_stats['last_check'] != null)
              Text(
                'آخر فحص: ${_formatDateTime(_stats['last_check'])}',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
      ),
    );
  }

  Widget _buildMainSettingsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.settings, color: Colors.purple),
                SizedBox(width: 8),
                Text(
                  'الإعدادات الأساسية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('تفعيل الإشعارات الذكية'),
              subtitle: const Text('تشغيل/إيقاف نظام الإشعارات الذكي'),
              value: _settings['enabled'] ?? true,
              onChanged: (value) {
                setState(() {
                  _settings['enabled'] = value;
                });
                _saveSettings();
              },
            ),
            const Divider(),
            SwitchListTile(
              title: const Text('الوضع العاجل فقط'),
              subtitle: const Text('إرسال الإشعارات عالية الأولوية فقط'),
              value: _settings['urgent_only_mode'] ?? false,
              onChanged:
                  _settings['enabled'] == true
                      ? (value) {
                        setState(() {
                          _settings['urgent_only_mode'] = value;
                        });
                        _saveSettings();
                      }
                      : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationTypesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.notifications_active, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'أنواع الإشعارات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('إشعارات الديون المتأخرة'),
              subtitle: const Text('تنبيهات للديون المتجاوزة لتاريخ الاستحقاق'),
              value: _settings['overdue_notifications'] ?? true,
              onChanged:
                  _settings['enabled'] == true
                      ? (value) {
                        setState(() {
                          _settings['overdue_notifications'] = value;
                        });
                        _saveSettings();
                      }
                      : null,
            ),
            SwitchListTile(
              title: const Text('إشعارات الديون المستحقة قريباً'),
              subtitle: const Text('تنبيهات للديون المستحقة خلال 3 أيام'),
              value: _settings['due_soon_notifications'] ?? true,
              onChanged:
                  _settings['enabled'] == true
                      ? (value) {
                        setState(() {
                          _settings['due_soon_notifications'] = value;
                        });
                        _saveSettings();
                      }
                      : null,
            ),
            SwitchListTile(
              title: const Text('تذكيرات الدفع'),
              subtitle: const Text('تنبيهات للديون بدون نشاط لفترة طويلة'),
              value: _settings['payment_reminders'] ?? true,
              onChanged:
                  _settings['enabled'] == true
                      ? (value) {
                        setState(() {
                          _settings['payment_reminders'] = value;
                        });
                        _saveSettings();
                      }
                      : null,
            ),
            SwitchListTile(
              title: const Text('الملخص الأسبوعي'),
              subtitle: const Text('ملخص أسبوعي لحالة الديون'),
              value: _settings['weekly_summary'] ?? true,
              onChanged:
                  _settings['enabled'] == true
                      ? (value) {
                        setState(() {
                          _settings['weekly_summary'] = value;
                        });
                        _saveSettings();
                      }
                      : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimingSettingsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.schedule, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  'إعدادات التوقيت',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('فترة الفحص'),
              subtitle: Text(
                'كل ${_settings['check_interval_hours'] ?? 6} ساعات',
              ),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _settings['enabled'] == true ? _showIntervalDialog : null,
            ),
            const Divider(),
            ListTile(
              title: const Text('الساعات الهادئة'),
              subtitle: Text(
                'من ${_settings['quiet_hours_start'] ?? 22}:00 إلى ${_settings['quiet_hours_end'] ?? 8}:00',
              ),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap:
                  _settings['enabled'] == true ? _showQuietHoursDialog : null,
            ),
            const Divider(),
            ListTile(
              title: const Text('الحد الأقصى للإشعارات اليومية'),
              subtitle: Text(
                '${_settings['max_notifications_per_day'] ?? 5} إشعارات',
              ),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap:
                  _settings['enabled'] == true
                      ? _showMaxNotificationsDialog
                      : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSettingsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.tune, color: Colors.red),
                SizedBox(width: 8),
                Text(
                  'إعدادات متقدمة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('تصعيد الإشعارات'),
              subtitle: const Text(
                'زيادة تكرار الإشعارات للديون المتأخرة جداً',
              ),
              value: _settings['escalation_enabled'] ?? true,
              onChanged:
                  _settings['enabled'] == true
                      ? (value) {
                        setState(() {
                          _settings['escalation_enabled'] = value;
                        });
                        _saveSettings();
                      }
                      : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () async {
              await SmartNotificationService.checkOverdueDebtsNow();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم فحص الديون المتأخرة'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            icon: const Icon(Icons.search),
            label: const Text('فحص فوري للديون المتأخرة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () async {
              await SmartNotificationService.sendWeeklySummary();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم إرسال الملخص الأسبوعي'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            icon: const Icon(Icons.summarize),
            label: const Text('إرسال ملخص أسبوعي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _showClearHistoryDialog,
            icon: const Icon(Icons.clear_all),
            label: const Text('مسح تاريخ الإشعارات'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  String _formatDateTime(String? dateTimeString) {
    if (dateTimeString == null) return 'غير محدد';
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'غير صحيح';
    }
  }

  void _showIntervalDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('فترة الفحص'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('اختر فترة الفحص الدوري للديون:'),
                const SizedBox(height: 16),
                ...([1, 3, 6, 12, 24].map(
                  (hours) => RadioListTile<int>(
                    title: Text('كل $hours ${hours == 1 ? 'ساعة' : 'ساعات'}'),
                    value: hours,
                    groupValue: _settings['check_interval_hours'],
                    onChanged: (value) {
                      setState(() {
                        _settings['check_interval_hours'] = value;
                      });
                      Navigator.pop(context);
                      _saveSettings();
                    },
                  ),
                )),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  void _showQuietHoursDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('الساعات الهادئة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('تحديد الساعات التي لا يتم إرسال إشعارات فيها:'),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Text('من: '),
                    DropdownButton<int>(
                      value: _settings['quiet_hours_start'],
                      items: List.generate(
                        24,
                        (index) => DropdownMenuItem(
                          value: index,
                          child: Text('${index.toString().padLeft(2, '0')}:00'),
                        ),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _settings['quiet_hours_start'] = value;
                        });
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Text('إلى: '),
                    DropdownButton<int>(
                      value: _settings['quiet_hours_end'],
                      items: List.generate(
                        24,
                        (index) => DropdownMenuItem(
                          value: index,
                          child: Text('${index.toString().padLeft(2, '0')}:00'),
                        ),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _settings['quiet_hours_end'] = value;
                        });
                      },
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _saveSettings();
                },
                child: const Text('حفظ'),
              ),
            ],
          ),
    );
  }

  void _showMaxNotificationsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('الحد الأقصى للإشعارات اليومية'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('اختر الحد الأقصى لعدد الإشعارات في اليوم:'),
                const SizedBox(height: 16),
                ...([1, 3, 5, 10, 15, 20].map(
                  (count) => RadioListTile<int>(
                    title: Text('$count ${count == 1 ? 'إشعار' : 'إشعارات'}'),
                    value: count,
                    groupValue: _settings['max_notifications_per_day'],
                    onChanged: (value) {
                      setState(() {
                        _settings['max_notifications_per_day'] = value;
                      });
                      Navigator.pop(context);
                      _saveSettings();
                    },
                  ),
                )),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  void _showClearHistoryDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('مسح تاريخ الإشعارات'),
            content: const Text(
              'هل أنت متأكد من رغبتك في مسح تاريخ الإشعارات؟\n'
              'سيتم حذف جميع الإحصائيات والسجلات.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await SmartNotificationService.clearNotificationHistory();
                  await _loadStats();
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم مسح تاريخ الإشعارات'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('مسح', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
    );
  }
}
