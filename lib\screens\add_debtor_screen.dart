import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/debtor_provider.dart';
import '../providers/settings_provider.dart';
import '../models/debtor.dart';
import '../widgets/custom_form_field.dart';
import '../widgets/common_widgets.dart';
import '../widgets/enhanced_ui_components.dart';
import '../widgets/responsive_layout.dart';
import '../widgets/enhanced_form.dart';
import '../utils/ui_enhancement_service.dart';
import '../services/notification_service.dart';
import '../widgets/overflow_safe_wrapper.dart';
import '../utils/app_theme.dart';

class AddDebtorScreen extends StatefulWidget {
  final Debtor? debtor; // For editing existing debtor

  const AddDebtorScreen({super.key, this.debtor});

  @override
  State<AddDebtorScreen> createState() => _AddDebtorScreenState();
}

class _AddDebtorScreenState extends State<AddDebtorScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _totalDebtController = TextEditingController();
  final _notesController = TextEditingController();
  final _dateController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  DateTime? _selectedDueDate; // تاريخ التسديد المطلوب
  bool _isLoading = false;

  bool get _isEditing => widget.debtor != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _initializeForEditing();
    } else {
      _dateController.text =
          '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}';
    }
  }

  void _initializeForEditing() {
    final debtor = widget.debtor!;
    _nameController.text = debtor.name;
    _totalDebtController.text = debtor.totalDebt.toString();
    _notesController.text = debtor.notes ?? '';
    _selectedDate = debtor.createdAt;
    _selectedDueDate = debtor.dueDate; // تهيئة تاريخ التسديد
    _dateController.text =
        '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _totalDebtController.dispose();
    _notesController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return EnhancedScaffold(
          appBar: CustomAppBar(
            title:
                _isEditing
                    ? (settings.language == AppLanguage.arabic
                        ? 'تعديل المدين'
                        : 'Edit Debtor')
                    : (settings.language == AppLanguage.arabic
                        ? 'إضافة مدين جديد'
                        : 'Add New Debtor'),
          ),
          body: Form(
            key: _formKey,
            child: ResponsiveWrapper(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Name field
                  CustomFormField(
                    label:
                        settings.language == AppLanguage.arabic
                            ? 'اسم المدين *'
                            : 'Debtor Name *',
                    hint:
                        settings.language == AppLanguage.arabic
                            ? 'أدخل اسم المدين'
                            : 'Enter debtor name',
                    controller: _nameController,
                    validator: _validateName,
                    prefixIcon: const Icon(Icons.person),
                    keyboardType: TextInputType.name,
                  ),

                  const SizedBox(height: 24),

                  // Total debt field
                  CustomNumberFormField(
                    label: 'المبلغ الأصلي *',
                    hint: 'أدخل المبلغ الأصلي للدين',
                    controller: _totalDebtController,
                    validator: _validateTotalDebt,
                    prefixIcon: const Icon(Icons.attach_money),
                    suffix: 'د.ع',
                    allowDecimals: true,
                  ),

                  const SizedBox(height: 24),

                  // Date field
                  CustomDateFormField(
                    label: 'تاريخ إنشاء الدين *',
                    hint: 'اختر تاريخ إنشاء الدين',
                    controller: _dateController,
                    validator: _validateDate,
                    initialDate: _selectedDate,
                    firstDate: DateTime(2000),
                    lastDate: DateTime.now(),
                    onDateSelected: (date) {
                      setState(() {
                        _selectedDate = date;
                      });
                    },
                  ),

                  const SizedBox(height: 24),

                  // Notes field
                  CustomTextAreaFormField(
                    label: 'ملاحظات',
                    hint: 'أدخل أي ملاحظات إضافية (اختياري)',
                    controller: _notesController,
                    maxLines: 4,
                    maxLength: 500,
                  ),

                  const SizedBox(height: 24),

                  // Due Date field
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.schedule,
                                color: Theme.of(context).primaryColor,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'تاريخ التسديد المطلوب (اختياري)',
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  _selectedDueDate != null
                                      ? '${_selectedDueDate!.day}/${_selectedDueDate!.month}/${_selectedDueDate!.year}'
                                      : 'لم يتم تحديد تاريخ',
                                  style: Theme.of(context).textTheme.bodyLarge,
                                ),
                              ),
                              TextButton.icon(
                                onPressed: _selectDueDate,
                                icon: const Icon(Icons.calendar_today),
                                label: Text(
                                  _selectedDueDate != null ? 'تغيير' : 'تحديد',
                                ),
                              ),
                              if (_selectedDueDate != null)
                                TextButton.icon(
                                  onPressed: () {
                                    setState(() {
                                      _selectedDueDate = null;
                                    });
                                  },
                                  icon: const Icon(Icons.clear),
                                  label: const Text('إزالة'),
                                  style: TextButton.styleFrom(
                                    foregroundColor: Colors.red,
                                  ),
                                ),
                            ],
                          ),
                          if (_selectedDueDate != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: Text(
                                _getDueDateInfo(),
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(color: _getDueDateColor()),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed:
                              _isLoading
                                  ? null
                                  : () => Navigator.of(context).pop(),
                          child: const Text('إلغاء'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _saveDebtor,
                          child:
                              _isLoading
                                  ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  )
                                  : Text(_isEditing ? 'تحديث' : 'إضافة'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Help text
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.blue[700],
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'معلومات مهمة:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue[700],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• المبلغ الأصلي هو المبلغ الأساسي للدين\n'
                          '• يمكنك إضافة عناصر إضافية ومدفوعات لاحقاً\n'
                          '• سيتم حساب المبلغ المتبقي تلقائياً',
                          style: TextStyle(color: Colors.blue[700]),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // اختيار تاريخ التسديد
  Future<void> _selectDueDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedDueDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
      helpText: 'اختر تاريخ التسديد المطلوب',
      cancelText: 'إلغاء',
      confirmText: 'تأكيد',
    );

    if (picked != null && picked != _selectedDueDate) {
      setState(() {
        _selectedDueDate = picked;
      });
    }
  }

  // الحصول على معلومات تاريخ التسديد
  String _getDueDateInfo() {
    if (_selectedDueDate == null) return '';

    final now = DateTime.now();
    final difference = _selectedDueDate!.difference(now).inDays;

    if (difference > 0) {
      return 'باقي $difference يوم على التسديد';
    } else if (difference == 0) {
      return 'التسديد اليوم';
    } else {
      return 'متأخر ${difference.abs()} يوم';
    }
  }

  // الحصول على لون تاريخ التسديد
  Color _getDueDateColor() {
    if (_selectedDueDate == null) return Colors.grey;

    final now = DateTime.now();
    final difference = _selectedDueDate!.difference(now).inDays;

    if (difference > 7) {
      return Colors.green; // أكثر من أسبوع
    } else if (difference > 0) {
      return Colors.orange; // أقل من أسبوع
    } else {
      return Colors.red; // متأخر
    }
  }

  String? _validateName(String? value) {
    final settings = Provider.of<SettingsProvider>(context, listen: false);
    if (value == null || value.trim().isEmpty) {
      return settings.language == AppLanguage.arabic
          ? 'اسم المدين مطلوب'
          : 'Debtor name is required';
    }
    if (value.trim().length < 2) {
      return settings.language == AppLanguage.arabic
          ? 'اسم المدين يجب أن يكون أكثر من حرف واحد'
          : 'Debtor name must be more than one character';
    }
    if (value.trim().length > 50) {
      return settings.language == AppLanguage.arabic
          ? 'اسم المدين يجب أن يكون أقل من 50 حرف'
          : 'Debtor name must be less than 50 characters';
    }
    return null;
  }

  String? _validateTotalDebt(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'المبلغ الأصلي مطلوب';
    }

    final amount = double.tryParse(value);
    if (amount == null) {
      return 'يرجى إدخال مبلغ صحيح';
    }

    if (amount < 0) {
      return 'المبلغ يجب أن يكون أكبر من أو يساوي صفر';
    }

    if (amount > 999999999) {
      return 'المبلغ كبير جداً';
    }

    return null;
  }

  String? _validateDate(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'تاريخ إنشاء الدين مطلوب';
    }
    return null;
  }

  Future<void> _saveDebtor() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final provider = Provider.of<DebtorProvider>(context, listen: false);

      final debtor = Debtor(
        id: _isEditing ? widget.debtor!.id : null,
        name: _nameController.text.trim(),
        totalDebt: double.parse(_totalDebtController.text),
        createdAt: _selectedDate,
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
        lastActivity: _isEditing ? widget.debtor!.lastActivity : DateTime.now(),
        dueDate: _selectedDueDate, // إضافة تاريخ التسديد
      );

      if (_isEditing) {
        await provider.updateDebtor(debtor);
      } else {
        await provider.addDebtor(debtor);

        // Show notification for new debtor
        await NotificationService.showItemAddedNotification(
          debtorName: debtor.name,
          itemName: 'دين جديد',
          itemPrice: debtor.totalDebt,
          debtorId: debtor.id,
        );
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing ? 'تم تحديث المدين بنجاح' : 'تم إضافة المدين بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
