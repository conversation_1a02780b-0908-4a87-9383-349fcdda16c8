import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/debtor.dart';
import '../services/smart_qr_service.dart';
import '../services/database_service.dart';
import '../utils/ui_enhancement_service.dart';
import '../widgets/enhanced_form.dart';

/// شاشة إنشاء ومشاركة QR
class QRGeneratorScreen extends StatefulWidget {
  final Debtor? debtor; // إذا كان محدد، ينشئ QR للمدين

  const QRGeneratorScreen({
    super.key,
    this.debtor,
  });

  @override
  State<QRGeneratorScreen> createState() => _QRGeneratorScreenState();
}

class _QRGeneratorScreenState extends State<QRGeneratorScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  // للمدين المحدد
  bool includeDetails = true;
  bool includePaymentHistory = false;
  
  // للملخص العام
  bool isGenerating = false;
  String? qrData;
  Uint8List? qrImageBytes;
  
  // إعدادات التصميم
  Color qrForegroundColor = Colors.black;
  Color qrBackgroundColor = Colors.white;
  Color primaryColor = const Color(0xFF9C27B0);
  double qrSize = 300;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.debtor != null ? 1 : 2,
      vsync: this,
    );
    
    if (widget.debtor != null) {
      _generateDebtorQR();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UIEnhancementService.enhancedAppBar(
        title: widget.debtor != null 
            ? 'QR للمدين: ${widget.debtor!.name}'
            : 'مولد QR الذكي',
        actions: [
          if (qrImageBytes != null)
            IconButton(
              onPressed: _shareQR,
              icon: const Icon(Icons.share),
              tooltip: 'مشاركة QR',
            ),
        ],
      ),
      body: UIEnhancementService.safeWrapper(
        child: Column(
          children: [
            if (widget.debtor == null) _buildTabBar(),
            Expanded(
              child: widget.debtor != null
                  ? _buildDebtorQRTab()
                  : TabBarView(
                      controller: _tabController,
                      children: [
                        _buildSummaryQRTab(),
                        _buildCustomQRTab(),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: primaryColor,
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey.shade600,
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard),
            text: 'ملخص عام',
          ),
          Tab(
            icon: Icon(Icons.tune),
            text: 'مخصص',
          ),
        ],
      ),
    );
  }

  Widget _buildDebtorQRTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // إعدادات المحتوى
          UIEnhancementService.enhancedCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'إعدادات المحتوى',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                SwitchListTile(
                  title: const Text('تضمين تفاصيل العناصر'),
                  subtitle: const Text('يشمل قائمة بجميع عناصر الدين'),
                  value: includeDetails,
                  onChanged: (value) {
                    setState(() {
                      includeDetails = value;
                    });
                    _generateDebtorQR();
                  },
                ),
                
                SwitchListTile(
                  title: const Text('تضمين تاريخ الدفعات'),
                  subtitle: const Text('يشمل تفاصيل جميع الدفعات'),
                  value: includePaymentHistory,
                  onChanged: (value) {
                    setState(() {
                      includePaymentHistory = value;
                    });
                    _generateDebtorQR();
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // إعدادات التصميم
          _buildDesignSettings(),
          
          const SizedBox(height: 16),
          
          // عرض QR
          _buildQRDisplay(),
          
          const SizedBox(height: 16),
          
          // أزرار الإجراءات
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildSummaryQRTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          UIEnhancementService.enhancedCard(
            child: Column(
              children: [
                const Icon(
                  Icons.dashboard,
                  size: 64,
                  color: Colors.blue,
                ),
                const SizedBox(height: 16),
                const Text(
                  'ملخص إحصائيات عام',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'ينشئ QR يحتوي على ملخص شامل لجميع الديون والإحصائيات',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                UIEnhancementService.enhancedButton(
                  text: 'إنشاء ملخص QR',
                  onPressed: _generateSummaryQR,
                  icon: Icons.qr_code,
                  isLoading: isGenerating,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          if (qrData != null) ...[
            _buildDesignSettings(),
            const SizedBox(height: 16),
            _buildQRDisplay(),
            const SizedBox(height: 16),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }

  Widget _buildCustomQRTab() {
    return const Center(
      child: Text(
        'إعدادات QR مخصص\n(قيد التطوير)',
        style: TextStyle(fontSize: 16),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildDesignSettings() {
    return UIEnhancementService.enhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات التصميم',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // حجم QR
          Text(
            'حجم QR: ${qrSize.toInt()}px',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Slider(
            value: qrSize,
            min: 200,
            max: 500,
            divisions: 6,
            onChanged: (value) {
              setState(() {
                qrSize = value;
              });
              _regenerateQRImage();
            },
          ),
          
          const SizedBox(height: 16),
          
          // ألوان
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('لون المقدمة'),
                    const SizedBox(height: 8),
                    GestureDetector(
                      onTap: () => _showColorPicker(true),
                      child: Container(
                        height: 40,
                        decoration: BoxDecoration(
                          color: qrForegroundColor,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('لون الخلفية'),
                    const SizedBox(height: 8),
                    GestureDetector(
                      onTap: () => _showColorPicker(false),
                      child: Container(
                        height: 40,
                        decoration: BoxDecoration(
                          color: qrBackgroundColor,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQRDisplay() {
    if (qrData == null) {
      return UIEnhancementService.enhancedCard(
        child: Container(
          height: 300,
          child: const Center(
            child: Text(
              'لم يتم إنشاء QR بعد',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
          ),
        ),
      );
    }

    return UIEnhancementService.enhancedCard(
      child: Column(
        children: [
          const Text(
            'رمز QR المُنشأ',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          if (qrImageBytes != null)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: qrBackgroundColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Image.memory(
                qrImageBytes!,
                width: qrSize,
                height: qrSize,
              ),
            )
          else
            Container(
              width: qrSize,
              height: qrSize,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: UIEnhancementService.enhancedButton(
            text: 'حفظ كصورة',
            onPressed: qrImageBytes != null ? _saveQRImage : null,
            icon: Icons.save,
            isOutlined: true,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: UIEnhancementService.enhancedButton(
            text: 'مشاركة',
            onPressed: qrImageBytes != null ? _shareQR : null,
            icon: Icons.share,
          ),
        ),
      ],
    );
  }

  void _generateDebtorQR() async {
    if (widget.debtor == null) return;
    
    setState(() {
      isGenerating = true;
    });
    
    try {
      final data = await SmartQRService.generateDebtorQR(
        debtor: widget.debtor!,
        includeDetails: includeDetails,
        includePaymentHistory: includePaymentHistory,
      );
      
      setState(() {
        qrData = data;
      });
      
      await _regenerateQRImage();
    } catch (e) {
      _showErrorDialog('خطأ في إنشاء QR: $e');
    } finally {
      setState(() {
        isGenerating = false;
      });
    }
  }

  void _generateSummaryQR() async {
    setState(() {
      isGenerating = true;
    });
    
    try {
      final data = await SmartQRService.generateSummaryQR();
      
      setState(() {
        qrData = data;
      });
      
      await _regenerateQRImage();
    } catch (e) {
      _showErrorDialog('خطأ في إنشاء QR: $e');
    } finally {
      setState(() {
        isGenerating = false;
      });
    }
  }

  Future<void> _regenerateQRImage() async {
    if (qrData == null) return;
    
    try {
      final imageBytes = await SmartQRService.generateAdvancedQRCard(
        data: qrData!,
        title: widget.debtor != null 
            ? 'معلومات المدين: ${widget.debtor!.name}'
            : 'ملخص الإحصائيات',
        subtitle: 'كوزمتك وكماليات باسم',
        description: 'امسح هذا الرمز لعرض التفاصيل',
        primaryColor: primaryColor,
      );
      
      setState(() {
        qrImageBytes = imageBytes;
      });
    } catch (e) {
      _showErrorDialog('خطأ في إنشاء صورة QR: $e');
    }
  }

  void _showColorPicker(bool isForeground) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isForeground ? 'اختر لون المقدمة' : 'اختر لون الخلفية'),
        content: SingleChildScrollView(
          child: BlockPicker(
            pickerColor: isForeground ? qrForegroundColor : qrBackgroundColor,
            onColorChanged: (color) {
              setState(() {
                if (isForeground) {
                  qrForegroundColor = color;
                } else {
                  qrBackgroundColor = color;
                }
              });
              Navigator.pop(context);
              _regenerateQRImage();
            },
          ),
        ),
      ),
    );
  }

  void _saveQRImage() async {
    if (qrImageBytes == null) return;
    
    try {
      // يمكن إضافة منطق حفظ الصورة هنا
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ الصورة بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      _showErrorDialog('خطأ في حفظ الصورة: $e');
    }
  }

  void _shareQR() async {
    if (qrImageBytes == null) return;
    
    try {
      final fileName = widget.debtor != null 
          ? 'qr_${widget.debtor!.name}_${DateTime.now().millisecondsSinceEpoch}'
          : 'qr_summary_${DateTime.now().millisecondsSinceEpoch}';
      
      await SmartQRService.shareQRImage(
        imageBytes: qrImageBytes!,
        fileName: fileName,
        text: widget.debtor != null 
            ? 'معلومات المدين: ${widget.debtor!.name}'
            : 'ملخص إحصائيات الديون',
      );
    } catch (e) {
      _showErrorDialog('خطأ في مشاركة QR: $e');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}

// مكون اختيار الألوان البسيط
class BlockPicker extends StatelessWidget {
  final Color pickerColor;
  final ValueChanged<Color> onColorChanged;

  const BlockPicker({
    super.key,
    required this.pickerColor,
    required this.onColorChanged,
  });

  @override
  Widget build(BuildContext context) {
    final colors = [
      Colors.black,
      Colors.white,
      Colors.red,
      Colors.green,
      Colors.blue,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
      Colors.brown,
      Colors.grey,
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: colors.map((color) {
        return GestureDetector(
          onTap: () => onColorChanged(color),
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: pickerColor == color ? Colors.black : Colors.grey.shade300,
                width: pickerColor == color ? 3 : 1,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
