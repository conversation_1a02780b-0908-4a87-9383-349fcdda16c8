import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/smart_qr_service.dart';
import '../widgets/qr_result_dialog.dart';
import '../utils/ui_enhancement_service.dart';

/// شاشة ماسح QR المتطور
class AdvancedQRScannerScreen extends StatefulWidget {
  const AdvancedQRScannerScreen({super.key});

  @override
  State<AdvancedQRScannerScreen> createState() => _AdvancedQRScannerScreenState();
}

class _AdvancedQRScannerScreenState extends State<AdvancedQRScannerScreen>
    with TickerProviderStateMixin {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  Barcode? result;
  bool isFlashOn = false;
  bool isScanning = true;
  bool isProcessing = false;
  
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _scanAnimation;
  late Animation<double> _pulseAnimation;
  
  Timer? _scanTimer;
  String _scanMode = 'auto'; // auto, manual, gallery

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _requestPermissions();
  }

  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _scanAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.repeat();
    _pulseController.repeat(reverse: true);
  }

  Future<void> _requestPermissions() async {
    final status = await Permission.camera.request();
    if (status.isDenied) {
      _showPermissionDialog();
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إذن الكاميرا مطلوب'),
        content: const Text('يحتاج التطبيق إلى إذن الكاميرا لمسح رموز QR'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text('الإعدادات'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: UIEnhancementService.safeWrapper(
        child: Stack(
          children: [
            // كاميرا QR
            _buildQRView(),
            
            // طبقة التحكم العلوية
            _buildTopControls(),
            
            // إطار المسح
            _buildScanFrame(),
            
            // أدوات التحكم السفلية
            _buildBottomControls(),
            
            // مؤشر المعالجة
            if (isProcessing) _buildProcessingIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildQRView() {
    return QRView(
      key: qrKey,
      onQRViewCreated: _onQRViewCreated,
      overlay: QrScannerOverlayShape(
        borderColor: Colors.transparent,
        borderRadius: 20,
        borderLength: 0,
        borderWidth: 0,
        cutOutSize: MediaQuery.of(context).size.width * 0.7,
      ),
      onPermissionSet: (ctrl, p) => _onPermissionSet(context, ctrl, p),
    );
  }

  Widget _buildTopControls() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withOpacity(0.7),
              Colors.transparent,
            ],
          ),
        ),
        child: Row(
          children: [
            // زر الرجوع
            IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back, color: Colors.white, size: 28),
            ),
            
            const Spacer(),
            
            // عنوان الشاشة
            const Text(
              'ماسح QR الذكي',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const Spacer(),
            
            // زر الفلاش
            IconButton(
              onPressed: _toggleFlash,
              icon: Icon(
                isFlashOn ? Icons.flash_on : Icons.flash_off,
                color: Colors.white,
                size: 28,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScanFrame() {
    return Center(
      child: SizedBox(
        width: MediaQuery.of(context).size.width * 0.7,
        height: MediaQuery.of(context).size.width * 0.7,
        child: Stack(
          children: [
            // إطار المسح المتحرك
            AnimatedBuilder(
              animation: _scanAnimation,
              builder: (context, child) {
                return CustomPaint(
                  size: Size.infinite,
                  painter: ScanFramePainter(
                    progress: _scanAnimation.value,
                    isScanning: isScanning,
                  ),
                );
              },
            ),
            
            // زوايا الإطار
            _buildCorners(),
            
            // خط المسح المتحرك
            if (isScanning) _buildScanLine(),
          ],
        ),
      ),
    );
  }

  Widget _buildCorners() {
    const cornerSize = 30.0;
    const cornerThickness = 4.0;
    const cornerColor = Colors.white;
    
    return Stack(
      children: [
        // الزاوية العلوية اليسرى
        Positioned(
          top: 0,
          left: 0,
          child: Container(
            width: cornerSize,
            height: cornerSize,
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: cornerColor, width: cornerThickness),
                left: BorderSide(color: cornerColor, width: cornerThickness),
              ),
            ),
          ),
        ),
        
        // الزاوية العلوية اليمنى
        Positioned(
          top: 0,
          right: 0,
          child: Container(
            width: cornerSize,
            height: cornerSize,
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: cornerColor, width: cornerThickness),
                right: BorderSide(color: cornerColor, width: cornerThickness),
              ),
            ),
          ),
        ),
        
        // الزاوية السفلية اليسرى
        Positioned(
          bottom: 0,
          left: 0,
          child: Container(
            width: cornerSize,
            height: cornerSize,
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: cornerColor, width: cornerThickness),
                left: BorderSide(color: cornerColor, width: cornerThickness),
              ),
            ),
          ),
        ),
        
        // الزاوية السفلية اليمنى
        Positioned(
          bottom: 0,
          right: 0,
          child: Container(
            width: cornerSize,
            height: cornerSize,
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: cornerColor, width: cornerThickness),
                right: BorderSide(color: cornerColor, width: cornerThickness),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildScanLine() {
    return AnimatedBuilder(
      animation: _scanAnimation,
      builder: (context, child) {
        return Positioned(
          top: (MediaQuery.of(context).size.width * 0.7 - 4) * _scanAnimation.value,
          left: 0,
          right: 0,
          child: Container(
            height: 4,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  Colors.green.withOpacity(0.8),
                  Colors.green,
                  Colors.green.withOpacity(0.8),
                  Colors.transparent,
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.5),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomControls() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [
              Colors.black.withOpacity(0.8),
              Colors.transparent,
            ],
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // أزرار الوضع
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildModeButton(
                  icon: Icons.lightbulb_outline,
                  label: 'الضوء',
                  onTap: _toggleFlash,
                  isActive: isFlashOn,
                ),
                _buildModeButton(
                  icon: Icons.image,
                  label: 'مسح الصور',
                  onTap: _scanFromGallery,
                ),
                _buildModeButton(
                  icon: Icons.qr_code,
                  label: 'التركيز الذكي',
                  onTap: _toggleAutoFocus,
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // شريط التحكم في التكبير
            _buildZoomControls(),
            
            const SizedBox(height: 20),
            
            // نص الإرشادات
            Text(
              isScanning 
                ? 'وجه الكاميرا نحو رمز QR للمسح'
                : 'تم إيقاف المسح مؤقتاً',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModeButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isActive = false,
  }) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: isActive ? _pulseAnimation.value : 1.0,
          child: GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              onTap();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: isActive 
                    ? Colors.green.withOpacity(0.3)
                    : Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isActive ? Colors.green : Colors.white.withOpacity(0.3),
                  width: 2,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    icon,
                    color: isActive ? Colors.green : Colors.white,
                    size: 24,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    label,
                    style: TextStyle(
                      color: isActive ? Colors.green : Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildZoomControls() {
    return Row(
      children: [
        IconButton(
          onPressed: () => _adjustZoom(-0.1),
          icon: const Icon(Icons.zoom_out, color: Colors.white),
        ),
        Expanded(
          child: Container(
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
            child: FractionallySizedBox(
              widthFactor: 0.5, // يمكن ربطه بقيمة التكبير الفعلية
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
          ),
        ),
        IconButton(
          onPressed: () => _adjustZoom(0.1),
          icon: const Icon(Icons.zoom_in, color: Colors.white),
        ),
      ],
    );
  }

  Widget _buildProcessingIndicator() {
    return Container(
      color: Colors.black.withOpacity(0.7),
      child: const Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
            ),
            SizedBox(height: 16),
            Text(
              'جاري معالجة رمز QR...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onQRViewCreated(QRViewController controller) {
    setState(() {
      this.controller = controller;
    });
    
    controller.scannedDataStream.listen((scanData) {
      if (!isProcessing && isScanning) {
        _handleScanResult(scanData);
      }
    });
  }

  void _handleScanResult(Barcode scanData) async {
    if (scanData.code == null) return;
    
    setState(() {
      isProcessing = true;
      isScanning = false;
    });
    
    // إيقاف المسح مؤقتاً
    await controller?.pauseCamera();
    
    // اهتزاز للتأكيد
    HapticFeedback.mediumImpact();
    
    try {
      final qrData = scanData.code!;
      
      // التحقق من صحة QR
      if (SmartQRService.isValidDebtorTrackerQR(qrData)) {
        await _showQRResult(qrData);
      } else {
        await _showGenericQRResult(qrData);
      }
    } catch (e) {
      _showErrorDialog('خطأ في معالجة رمز QR: $e');
    } finally {
      setState(() {
        isProcessing = false;
        isScanning = true;
      });
      
      // استئناف المسح
      await controller?.resumeCamera();
    }
  }

  Future<void> _showQRResult(String qrData) async {
    await showDialog(
      context: context,
      builder: (context) => QRResultDialog(qrData: qrData),
    );
  }

  Future<void> _showGenericQRResult(String qrData) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('رمز QR عام'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('تم مسح رمز QR بنجاح:'),
            const SizedBox(height: 10),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                qrData,
                style: const TextStyle(fontFamily: 'monospace'),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          TextButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: qrData));
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم نسخ النص')),
              );
            },
            child: const Text('نسخ'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _toggleFlash() async {
    if (controller != null) {
      await controller!.toggleFlash();
      setState(() {
        isFlashOn = !isFlashOn;
      });
    }
  }

  void _toggleAutoFocus() async {
    // يمكن إضافة منطق التركيز التلقائي هنا
    HapticFeedback.lightImpact();
  }

  void _adjustZoom(double delta) async {
    // يمكن إضافة منطق التكبير هنا
    HapticFeedback.selectionClick();
  }

  void _scanFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);
      
      if (image != null) {
        // يمكن إضافة منطق مسح QR من الصورة هنا
        // باستخدام مكتبة مثل google_ml_kit
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('ميزة مسح الصور قيد التطوير')),
        );
      }
    } catch (e) {
      _showErrorDialog('خطأ في اختيار الصورة: $e');
    }
  }

  void _onPermissionSet(BuildContext context, QRViewController ctrl, bool p) {
    if (!p) {
      _showPermissionDialog();
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    _animationController.dispose();
    _pulseController.dispose();
    _scanTimer?.cancel();
    super.dispose();
  }
}

/// رسام إطار المسح المخصص
class ScanFramePainter extends CustomPainter {
  final double progress;
  final bool isScanning;

  ScanFramePainter({
    required this.progress,
    required this.isScanning,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (!isScanning) return;
    
    final paint = Paint()
      ..color = Colors.green.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    canvas.drawRect(rect, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
