import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import '../models/debtor.dart';
import '../services/smart_qr_service.dart';
import '../utils/ui_enhancement_service.dart';

/// شاشة عرض QR بسيطة للاختبار
class SimpleQRDisplayScreen extends StatefulWidget {
  final Debtor? debtor;

  const SimpleQRDisplayScreen({
    super.key,
    this.debtor,
  });

  @override
  State<SimpleQRDisplayScreen> createState() => _SimpleQRDisplayScreenState();
}

class _SimpleQRDisplayScreenState extends State<SimpleQRDisplayScreen> {
  String? qrData;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _generateQR();
  }

  void _generateQR() async {
    try {
      String data;
      if (widget.debtor != null) {
        data = await SmartQRService.generateDebtorQR(
          debtor: widget.debtor!,
          includeDetails: true,
          includePaymentHistory: false,
        );
      } else {
        data = await SmartQRService.generateSummaryQR();
      }
      
      setState(() {
        qrData = data;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = e.toString();
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UIEnhancementService.enhancedAppBar(
        title: widget.debtor != null 
            ? 'QR للمدين: ${widget.debtor!.name}'
            : 'QR ملخص الإحصائيات',
      ),
      body: UIEnhancementService.safeWrapper(
        child: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري إنشاء QR...'),
          ],
        ),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في إنشاء QR:',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 24),
            UIEnhancementService.enhancedButton(
              text: 'إعادة المحاولة',
              onPressed: () {
                setState(() {
                  isLoading = true;
                  errorMessage = null;
                });
                _generateQR();
              },
              icon: Icons.refresh,
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // عنوان
          Text(
            widget.debtor != null 
                ? 'QR كود للمدين: ${widget.debtor!.name}'
                : 'QR كود ملخص الإحصائيات',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 24),
          
          // QR Code
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: QrImageView(
              data: qrData!,
              version: QrVersions.auto,
              size: 250.0,
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              errorCorrectionLevel: QrErrorCorrectLevel.M,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // معلومات
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.blue.withOpacity(0.3),
              ),
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                  size: 32,
                ),
                const SizedBox(height: 8),
                const Text(
                  'كيفية الاستخدام',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'امسح هذا الرمز باستخدام تطبيق كوزمتك وكماليات باسم أو أي تطبيق قراءة QR لعرض المعلومات',
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // أزرار الإجراءات
          Row(
            children: [
              Expanded(
                child: UIEnhancementService.enhancedButton(
                  text: 'مشاركة',
                  onPressed: _shareQR,
                  icon: Icons.share,
                  isOutlined: true,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: UIEnhancementService.enhancedButton(
                  text: 'إنشاء جديد',
                  onPressed: () {
                    setState(() {
                      isLoading = true;
                    });
                    _generateQR();
                  },
                  icon: Icons.refresh,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _shareQR() async {
    if (qrData == null) return;
    
    try {
      // إنشاء صورة QR للمشاركة
      final imageBytes = await SmartQRService.generateQRImage(
        data: qrData!,
        size: 300,
      );
      
      final fileName = widget.debtor != null 
          ? 'qr_${widget.debtor!.name}_${DateTime.now().millisecondsSinceEpoch}'
          : 'qr_summary_${DateTime.now().millisecondsSinceEpoch}';
      
      await SmartQRService.shareQRImage(
        imageBytes: imageBytes,
        fileName: fileName,
        text: widget.debtor != null 
            ? 'QR كود للمدين: ${widget.debtor!.name}'
            : 'QR كود ملخص الإحصائيات',
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في مشاركة QR: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
