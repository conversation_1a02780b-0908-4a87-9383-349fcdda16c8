import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Service for applying UI enhancements across the app
class UIEnhancementService {
  static bool _isInitialized = false;

  /// Initialize UI enhancements
  static Future<void> initialize() async {
    if (_isInitialized) return;

    // Set system UI overlay style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    _isInitialized = true;
  }

  /// Apply overflow protection to any widget
  static Widget protectFromOverflow(Widget child, {
    EdgeInsets? padding,
    ScrollPhysics? physics,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          physics: physics ?? const BouncingScrollPhysics(),
          padding: padding,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight - (padding?.vertical ?? 0),
            ),
            child: IntrinsicHeight(child: child),
          ),
        );
      },
    );
  }

  /// Create responsive text that adapts to screen size
  static Widget responsiveText(
    String text, {
    TextStyle? style,
    int? maxLines,
    TextOverflow? overflow,
    TextAlign? textAlign,
    double? scaleFactor,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isSmallScreen = screenWidth < 400;
        final isMediumScreen = screenWidth < 600;

        double fontSize = style?.fontSize ?? 16;
        if (isSmallScreen) {
          fontSize *= 0.9;
        } else if (isMediumScreen) {
          fontSize *= 0.95;
        }

        if (scaleFactor != null) {
          fontSize *= scaleFactor;
        }

        return Text(
          text,
          style: style?.copyWith(fontSize: fontSize) ?? TextStyle(fontSize: fontSize),
          maxLines: maxLines,
          overflow: overflow ?? TextOverflow.ellipsis,
          textAlign: textAlign,
        );
      },
    );
  }

  /// Create responsive padding that adapts to screen size
  static EdgeInsets responsivePadding(BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    double padding;
    if (screenWidth < 600) {
      padding = mobile ?? 16;
    } else if (screenWidth < 1024) {
      padding = tablet ?? 24;
    } else {
      padding = desktop ?? 32;
    }

    return EdgeInsets.all(padding);
  }

  /// Create responsive margin that adapts to screen size
  static EdgeInsets responsiveMargin(BuildContext context, {
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    double margin;
    if (screenWidth < 600) {
      margin = mobile ?? 8;
    } else if (screenWidth < 1024) {
      margin = tablet ?? 16;
    } else {
      margin = desktop ?? 24;
    }

    return EdgeInsets.all(margin);
  }

  /// Create a flexible row that wraps on small screens
  static Widget flexibleRow({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    double spacing = 8,
    double breakpoint = 600,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= breakpoint) {
          // Wide screen: use Row
          return Row(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: _addSpacing(children, spacing, true),
          );
        } else {
          // Narrow screen: use Column
          return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: _addSpacing(children, spacing, false),
          );
        }
      },
    );
  }

  /// Add spacing between widgets
  static List<Widget> _addSpacing(List<Widget> widgets, double spacing, bool horizontal) {
    if (widgets.isEmpty) return widgets;
    
    final List<Widget> spacedWidgets = [];
    for (int i = 0; i < widgets.length; i++) {
      spacedWidgets.add(widgets[i]);
      if (i < widgets.length - 1) {
        spacedWidgets.add(
          horizontal
              ? SizedBox(width: spacing)
              : SizedBox(height: spacing),
        );
      }
    }
    return spacedWidgets;
  }

  /// Create a safe area wrapper with overflow protection
  static Widget safeWrapper({
    required Widget child,
    bool top = true,
    bool bottom = true,
    bool left = true,
    bool right = true,
    EdgeInsets? padding,
  }) {
    return SafeArea(
      top: top,
      bottom: bottom,
      left: left,
      right: right,
      child: protectFromOverflow(
        child,
        padding: padding,
      ),
    );
  }

  /// Create an enhanced app bar with consistent styling
  static PreferredSizeWidget enhancedAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    Color? backgroundColor,
    Color? foregroundColor,
    double? elevation,
  }) {
    return AppBar(
      title: responsiveText(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 20,
        ),
      ),
      actions: actions,
      leading: leading,
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? Colors.transparent,
      foregroundColor: foregroundColor,
      elevation: elevation ?? 0,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
  }

  /// Create an enhanced card with consistent styling
  static Widget enhancedCard({
    required Widget child,
    EdgeInsets? margin,
    EdgeInsets? padding,
    Color? backgroundColor,
    double? elevation,
    BorderRadius? borderRadius,
    VoidCallback? onTap,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 400;
        
        return Container(
          margin: margin ?? EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 12 : 16,
            vertical: 8,
          ),
          child: Material(
            color: backgroundColor ?? Theme.of(context).cardColor,
            borderRadius: borderRadius ?? BorderRadius.circular(isSmallScreen ? 12 : 16),
            elevation: elevation ?? 4,
            child: InkWell(
              onTap: onTap != null ? () {
                HapticFeedback.lightImpact();
                onTap();
              } : null,
              borderRadius: borderRadius ?? BorderRadius.circular(isSmallScreen ? 12 : 16),
              child: Padding(
                padding: padding ?? EdgeInsets.all(isSmallScreen ? 12 : 16),
                child: child,
              ),
            ),
          ),
        );
      },
    );
  }

  /// Create an enhanced button with consistent styling
  static Widget enhancedButton({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    Color? backgroundColor,
    Color? textColor,
    bool isOutlined = false,
    bool isLoading = false,
    Size? minimumSize,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 400;
        
        Widget buttonChild = Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isLoading)
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    textColor ?? Colors.white,
                  ),
                ),
              )
            else if (icon != null)
              Icon(icon, size: isSmallScreen ? 16 : 18),
            if ((icon != null || isLoading) && text.isNotEmpty)
              SizedBox(width: isSmallScreen ? 6 : 8),
            if (text.isNotEmpty)
              responsiveText(
                text,
                style: TextStyle(
                  color: textColor,
                  fontWeight: FontWeight.w600,
                  fontSize: isSmallScreen ? 14 : 16,
                ),
              ),
          ],
        );

        final padding = EdgeInsets.symmetric(
          horizontal: isSmallScreen ? 16 : 24,
          vertical: isSmallScreen ? 10 : 12,
        );

        if (isOutlined) {
          return OutlinedButton(
            onPressed: isLoading ? null : onPressed,
            style: OutlinedButton.styleFrom(
              padding: padding,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
              ),
              side: BorderSide(
                color: backgroundColor ?? Theme.of(context).primaryColor,
                width: 2,
              ),
              minimumSize: minimumSize,
            ),
            child: buttonChild,
          );
        }

        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? Theme.of(context).primaryColor,
            foregroundColor: textColor ?? Colors.white,
            padding: padding,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
            ),
            elevation: 4,
            minimumSize: minimumSize,
          ),
          child: buttonChild,
        );
      },
    );
  }

  /// Create an enhanced text field with consistent styling
  static Widget enhancedTextField({
    TextEditingController? controller,
    String? labelText,
    String? hintText,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixIconPressed,
    bool obscureText = false,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
    int? maxLines,
    bool enabled = true,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 400;
        
        return TextFormField(
          controller: controller,
          obscureText: obscureText,
          keyboardType: keyboardType,
          validator: validator,
          onChanged: onChanged,
          maxLines: maxLines ?? 1,
          enabled: enabled,
          style: TextStyle(
            fontSize: isSmallScreen ? 14 : 16,
          ),
          decoration: InputDecoration(
            labelText: labelText,
            hintText: hintText,
            prefixIcon: prefixIcon != null
                ? Icon(prefixIcon, size: isSmallScreen ? 20 : 24)
                : null,
            suffixIcon: suffixIcon != null
                ? IconButton(
                    icon: Icon(suffixIcon, size: isSmallScreen ? 20 : 24),
                    onPressed: onSuffixIconPressed,
                  )
                : null,
            contentPadding: EdgeInsets.symmetric(
              horizontal: isSmallScreen ? 12 : 16,
              vertical: isSmallScreen ? 12 : 16,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
          ),
        );
      },
    );
  }
}
