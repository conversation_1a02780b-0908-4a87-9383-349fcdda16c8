import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../services/smart_qr_service.dart';
import '../utils/ui_enhancement_service.dart';

/// مربع حوار لعرض نتائج مسح QR
class QRResultDialog extends StatefulWidget {
  final String qrData;

  const QRResultDialog({
    super.key,
    required this.qrData,
  });

  @override
  State<QRResultDialog> createState() => _QRResultDialogState();
}

class _QRResultDialogState extends State<QRResultDialog>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  DebtorQRInfo? debtorInfo;
  SummaryQRInfo? summaryInfo;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _processQRData();
  }

  void _initAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_fadeController);
    
    _slideController.forward();
    _fadeController.forward();
  }

  void _processQRData() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500)); // تأثير التحميل
      
      // محاولة استخراج معلومات المدين
      final debtor = SmartQRService.extractDebtorInfo(widget.qrData);
      if (debtor != null) {
        setState(() {
          debtorInfo = debtor;
          isLoading = false;
        });
        return;
      }
      
      // محاولة استخراج ملخص الإحصائيات
      final summary = SmartQRService.extractSummaryInfo(widget.qrData);
      if (summary != null) {
        setState(() {
          summaryInfo = summary;
          isLoading = false;
        });
        return;
      }
      
      // QR غير صالح
      setState(() {
        errorMessage = 'رمز QR غير صالح أو غير متوافق مع التطبيق';
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'خطأ في معالجة رمز QR: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: SlideTransition(
        position: _slideAnimation,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
              maxWidth: MediaQuery.of(context).size.width * 0.9,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(),
                Flexible(child: _buildContent()),
                _buildActions(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.qr_code_scanner,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'نتيجة مسح QR',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _getSubtitle(),
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  String _getSubtitle() {
    if (isLoading) return 'جاري المعالجة...';
    if (errorMessage != null) return 'خطأ في المعالجة';
    if (debtorInfo != null) return 'معلومات مدين';
    if (summaryInfo != null) return 'ملخص إحصائيات';
    return 'غير معروف';
  }

  Widget _buildContent() {
    if (isLoading) {
      return _buildLoadingContent();
    }
    
    if (errorMessage != null) {
      return _buildErrorContent();
    }
    
    if (debtorInfo != null) {
      return _buildDebtorContent();
    }
    
    if (summaryInfo != null) {
      return _buildSummaryContent();
    }
    
    return _buildErrorContent();
  }

  Widget _buildLoadingContent() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: const Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'جاري معالجة رمز QR...',
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorContent() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            errorMessage ?? 'خطأ غير معروف',
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDebtorContent() {
    final debtor = debtorInfo!;
    final currencyFormat = NumberFormat.currency(
      locale: 'ar_IQ',
      symbol: 'د.ع',
      decimalDigits: 0,
    );
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المدين الأساسية
          _buildInfoCard(
            title: 'معلومات المدين',
            icon: Icons.person,
            children: [
              _buildInfoRow('الاسم', debtor.name),
              if (debtor.phone != null) _buildInfoRow('الهاتف', debtor.phone!),
              if (debtor.address != null) _buildInfoRow('العنوان', debtor.address!),
              _buildInfoRow('تاريخ الإنشاء', _formatDate(debtor.createdAt)),
              if (debtor.dueDate != null)
                _buildInfoRow('تاريخ الاستحقاق', _formatDate(debtor.dueDate!)),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // الإحصائيات المالية
          _buildInfoCard(
            title: 'الإحصائيات المالية',
            icon: Icons.account_balance_wallet,
            children: [
              _buildInfoRow(
                'إجمالي الدين',
                currencyFormat.format(debtor.totalDebt),
                valueColor: Colors.red,
              ),
              _buildInfoRow(
                'المبلغ المدفوع',
                currencyFormat.format(debtor.totalPaid),
                valueColor: Colors.green,
              ),
              _buildInfoRow(
                'المبلغ المتبقي',
                currencyFormat.format(debtor.remainingDebt),
                valueColor: debtor.remainingDebt > 0 ? Colors.orange : Colors.green,
                isHighlighted: true,
              ),
              _buildInfoRow('نسبة السداد', '${debtor.paymentProgress.toStringAsFixed(1)}%'),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // إحصائيات إضافية
          _buildInfoCard(
            title: 'تفاصيل إضافية',
            icon: Icons.analytics,
            children: [
              _buildInfoRow('عدد العناصر', debtor.itemsCount.toString()),
              _buildInfoRow('عدد الدفعات', debtor.paymentsCount.toString()),
              _buildInfoRow(
                'حالة الاستحقاق',
                debtor.isOverdue ? 'متأخر' : 'في الموعد',
                valueColor: debtor.isOverdue ? Colors.red : Colors.green,
              ),
              if (debtor.isOverdue)
                _buildInfoRow(
                  'أيام التأخير',
                  '${debtor.daysOverdue} يوم',
                  valueColor: Colors.red,
                ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // معلومات QR
          _buildInfoCard(
            title: 'معلومات QR',
            icon: Icons.qr_code,
            children: [
              _buildInfoRow('تاريخ الإنشاء', _formatDateTime(debtor.timestamp)),
              _buildInfoRow('معرف المدين', debtor.id),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryContent() {
    final summary = summaryInfo!;
    final currencyFormat = NumberFormat.currency(
      locale: 'ar_IQ',
      symbol: 'د.ع',
      decimalDigits: 0,
    );
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            title: 'ملخص الإحصائيات العامة',
            icon: Icons.dashboard,
            children: [
              _buildInfoRow('إجمالي المديونين', summary.totalDebtors.toString()),
              _buildInfoRow(
                'إجمالي الديون المستحقة',
                currencyFormat.format(summary.totalOutstanding),
                valueColor: Colors.red,
                isHighlighted: true,
              ),
              _buildInfoRow(
                'إجمالي المبالغ المدفوعة',
                currencyFormat.format(summary.totalPaid),
                valueColor: Colors.green,
              ),
              _buildInfoRow(
                'المديونين المتأخرين',
                summary.overdueDebtors.toString(),
                valueColor: summary.overdueDebtors > 0 ? Colors.red : Colors.green,
              ),
              _buildInfoRow('إجمالي العناصر', summary.totalItems.toString()),
              _buildInfoRow('إجمالي الدفعات', summary.totalPayments.toString()),
            ],
          ),
          
          const SizedBox(height: 16),
          
          _buildInfoCard(
            title: 'معلومات الإنشاء',
            icon: Icons.info,
            children: [
              _buildInfoRow('تم الإنشاء بواسطة', summary.generatedBy),
              _buildInfoRow('تاريخ الإنشاء', _formatDateTime(summary.timestamp)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(children: children),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value, {
    Color? valueColor,
    bool isHighlighted = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                color: valueColor ?? Colors.black,
                fontSize: 14,
                fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: UIEnhancementService.enhancedButton(
              text: 'نسخ البيانات',
              onPressed: _copyData,
              icon: Icons.copy,
              isOutlined: true,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: UIEnhancementService.enhancedButton(
              text: 'إغلاق',
              onPressed: () => Navigator.pop(context),
              icon: Icons.close,
            ),
          ),
        ],
      ),
    );
  }

  void _copyData() {
    String textToCopy = '';
    
    if (debtorInfo != null) {
      final debtor = debtorInfo!;
      textToCopy = '''
معلومات المدين:
الاسم: ${debtor.name}
${debtor.phone != null ? 'الهاتف: ${debtor.phone}\n' : ''}
إجمالي الدين: ${NumberFormat.currency(locale: 'ar_IQ', symbol: 'د.ع', decimalDigits: 0).format(debtor.totalDebt)}
المبلغ المدفوع: ${NumberFormat.currency(locale: 'ar_IQ', symbol: 'د.ع', decimalDigits: 0).format(debtor.totalPaid)}
المبلغ المتبقي: ${NumberFormat.currency(locale: 'ar_IQ', symbol: 'د.ع', decimalDigits: 0).format(debtor.remainingDebt)}
نسبة السداد: ${debtor.paymentProgress.toStringAsFixed(1)}%
''';
    } else if (summaryInfo != null) {
      final summary = summaryInfo!;
      textToCopy = '''
ملخص الإحصائيات:
إجمالي المديونين: ${summary.totalDebtors}
إجمالي الديون المستحقة: ${NumberFormat.currency(locale: 'ar_IQ', symbol: 'د.ع', decimalDigits: 0).format(summary.totalOutstanding)}
إجمالي المبالغ المدفوعة: ${NumberFormat.currency(locale: 'ar_IQ', symbol: 'د.ع', decimalDigits: 0).format(summary.totalPaid)}
المديونين المتأخرين: ${summary.overdueDebtors}
''';
    }
    
    if (textToCopy.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: textToCopy));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ البيانات بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy', 'ar').format(date);
  }

  String _formatDateTime(DateTime date) {
    return DateFormat('dd/MM/yyyy HH:mm', 'ar').format(date);
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }
}
