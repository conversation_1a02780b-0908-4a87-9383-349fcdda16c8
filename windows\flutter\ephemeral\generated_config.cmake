# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\debtor_tracker" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter"
  "PROJECT_DIR=C:\\debtor_tracker"
  "FLUTTER_ROOT=C:\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\debtor_tracker\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\debtor_tracker"
  "FLUTTER_TARGET=C:\\debtor_tracker\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzUuMC0wLjAucHJl,RkxVVFRFUl9DSEFOTkVMPWJldGE=,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZDg2YjRlOWZkNQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049Zjg1ZjZiNjJiNg==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjAgKGJ1aWxkIDMuOS4wLTMzMy4yLmJldGEp"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\debtor_tracker\\.dart_tool\\package_config.json"
)
