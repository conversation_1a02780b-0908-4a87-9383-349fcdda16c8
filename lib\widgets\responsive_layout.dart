import 'package:flutter/material.dart';

/// Responsive layout system that adapts to different screen sizes
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final double mobileBreakpoint;
  final double tabletBreakpoint;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.mobileBreakpoint = 600,
    this.tabletBreakpoint = 1024,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= tabletBreakpoint && desktop != null) {
          return desktop!;
        } else if (constraints.maxWidth >= mobileBreakpoint && tablet != null) {
          return tablet!;
        } else {
          return mobile;
        }
      },
    );
  }
}

/// Enhanced Scaffold that prevents overflow and provides consistent layout
class EnhancedScaffold extends StatelessWidget {
  final PreferredSizeWidget? appBar;
  final Widget? body;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Widget? drawer;
  final Widget? endDrawer;
  final Widget? bottomNavigationBar;
  final Widget? bottomSheet;
  final Color? backgroundColor;
  final bool resizeToAvoidBottomInset;
  final bool extendBody;
  final bool extendBodyBehindAppBar;
  final EdgeInsets? padding;
  final bool enableSafeArea;

  const EnhancedScaffold({
    super.key,
    this.appBar,
    this.body,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.drawer,
    this.endDrawer,
    this.bottomNavigationBar,
    this.bottomSheet,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
    this.extendBody = false,
    this.extendBodyBehindAppBar = false,
    this.padding,
    this.enableSafeArea = true,
  });

  @override
  Widget build(BuildContext context) {
    Widget? bodyWidget = body;

    if (bodyWidget != null) {
      // Wrap body with overflow protection
      bodyWidget = OverflowProtectedBody(
        padding: padding,
        enableSafeArea: enableSafeArea,
        child: bodyWidget,
      );
    }

    return Scaffold(
      appBar: appBar,
      body: bodyWidget,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      drawer: drawer,
      endDrawer: endDrawer,
      bottomNavigationBar: bottomNavigationBar,
      bottomSheet: bottomSheet,
      backgroundColor: backgroundColor,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      extendBody: extendBody,
      extendBodyBehindAppBar: extendBodyBehindAppBar,
    );
  }
}

/// Body wrapper that prevents overflow issues
class OverflowProtectedBody extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final bool enableSafeArea;
  final ScrollPhysics? physics;

  const OverflowProtectedBody({
    super.key,
    required this.child,
    this.padding,
    this.enableSafeArea = true,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    Widget wrappedChild = child;

    // Add safe area if enabled
    if (enableSafeArea) {
      wrappedChild = SafeArea(child: wrappedChild);
    }

    // Add overflow protection with scrolling
    wrappedChild = LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          physics: physics ?? const BouncingScrollPhysics(),
          padding: padding,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight - (padding?.vertical ?? 0),
            ),
            child: IntrinsicHeight(child: wrappedChild),
          ),
        );
      },
    );

    return wrappedChild;
  }
}

/// Flexible grid layout that adapts to screen size
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int minItemsPerRow;
  final int maxItemsPerRow;
  final double minItemWidth;
  final double spacing;
  final double runSpacing;
  final EdgeInsets? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.minItemsPerRow = 1,
    this.maxItemsPerRow = 4,
    this.minItemWidth = 200,
    this.spacing = 8,
    this.runSpacing = 8,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth - (padding?.horizontal ?? 0);
        final itemsPerRow = _calculateItemsPerRow(availableWidth);
        
        return Padding(
          padding: padding ?? EdgeInsets.zero,
          child: Wrap(
            spacing: spacing,
            runSpacing: runSpacing,
            children: children.map((child) {
              final itemWidth = (availableWidth - (spacing * (itemsPerRow - 1))) / itemsPerRow;
              return SizedBox(
                width: itemWidth,
                child: child,
              );
            }).toList(),
          ),
        );
      },
    );
  }

  int _calculateItemsPerRow(double availableWidth) {
    final maxPossibleItems = (availableWidth / minItemWidth).floor();
    return maxPossibleItems.clamp(minItemsPerRow, maxItemsPerRow);
  }
}

/// Adaptive column layout that stacks on small screens
class AdaptiveColumns extends StatelessWidget {
  final List<Widget> children;
  final double breakpoint;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final double spacing;

  const AdaptiveColumns({
    super.key,
    required this.children,
    this.breakpoint = 600,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.spacing = 16,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= breakpoint) {
          // Wide screen: use Row
          return Row(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: _addSpacing(children, isHorizontal: true),
          );
        } else {
          // Narrow screen: use Column
          return Column(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: _addSpacing(children, isHorizontal: false),
          );
        }
      },
    );
  }

  List<Widget> _addSpacing(List<Widget> widgets, {required bool isHorizontal}) {
    if (widgets.isEmpty) return widgets;
    
    final List<Widget> spacedWidgets = [];
    for (int i = 0; i < widgets.length; i++) {
      spacedWidgets.add(widgets[i]);
      if (i < widgets.length - 1) {
        spacedWidgets.add(
          isHorizontal
              ? SizedBox(width: spacing)
              : SizedBox(height: spacing),
        );
      }
    }
    return spacedWidgets;
  }
}

/// Enhanced container with responsive padding and margins
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? mobilePadding;
  final EdgeInsets? tabletPadding;
  final EdgeInsets? desktopPadding;
  final EdgeInsets? mobileMargin;
  final EdgeInsets? tabletMargin;
  final EdgeInsets? desktopMargin;
  final double mobileBreakpoint;
  final double tabletBreakpoint;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.mobilePadding,
    this.tabletPadding,
    this.desktopPadding,
    this.mobileMargin,
    this.tabletMargin,
    this.desktopMargin,
    this.mobileBreakpoint = 600,
    this.tabletBreakpoint = 1024,
    this.backgroundColor,
    this.borderRadius,
    this.boxShadow,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        EdgeInsets padding;
        EdgeInsets margin;

        if (constraints.maxWidth >= tabletBreakpoint) {
          padding = desktopPadding ?? tabletPadding ?? mobilePadding ?? EdgeInsets.zero;
          margin = desktopMargin ?? tabletMargin ?? mobileMargin ?? EdgeInsets.zero;
        } else if (constraints.maxWidth >= mobileBreakpoint) {
          padding = tabletPadding ?? mobilePadding ?? EdgeInsets.zero;
          margin = tabletMargin ?? mobileMargin ?? EdgeInsets.zero;
        } else {
          padding = mobilePadding ?? EdgeInsets.zero;
          margin = mobileMargin ?? EdgeInsets.zero;
        }

        return Container(
          margin: margin,
          padding: padding,
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: borderRadius,
            boxShadow: boxShadow,
          ),
          child: child,
        );
      },
    );
  }
}

/// Scrollable content wrapper with keyboard awareness
class KeyboardAwareScrollView extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final ScrollPhysics? physics;
  final ScrollController? controller;
  final bool reverse;

  const KeyboardAwareScrollView({
    super.key,
    required this.child,
    this.padding,
    this.physics,
    this.controller,
    this.reverse = false,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final mediaQuery = MediaQuery.of(context);
        final keyboardHeight = mediaQuery.viewInsets.bottom;
        
        return SingleChildScrollView(
          controller: controller,
          physics: physics ?? const BouncingScrollPhysics(),
          reverse: reverse,
          padding: EdgeInsets.only(
            top: padding?.top ?? 0,
            left: padding?.left ?? 0,
            right: padding?.right ?? 0,
            bottom: (padding?.bottom ?? 0) + keyboardHeight,
          ),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight - keyboardHeight,
            ),
            child: IntrinsicHeight(child: child),
          ),
        );
      },
    );
  }
}

/// Enhanced dialog wrapper with responsive sizing
class ResponsiveDialog extends StatelessWidget {
  final Widget child;
  final String? title;
  final List<Widget>? actions;
  final EdgeInsets? contentPadding;
  final double? maxWidth;
  final double? maxHeight;

  const ResponsiveDialog({
    super.key,
    required this.child,
    this.title,
    this.actions,
    this.contentPadding,
    this.maxWidth,
    this.maxHeight,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: maxWidth ?? (isSmallScreen ? screenSize.width * 0.9 : 500),
          maxHeight: maxHeight ?? screenSize.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title != null)
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  title!,
                  style: Theme.of(context).textTheme.headlineSmall,
                  textAlign: TextAlign.center,
                ),
              ),
            Flexible(
              child: SingleChildScrollView(
                padding: contentPadding ?? const EdgeInsets.symmetric(horizontal: 16),
                child: child,
              ),
            ),
            if (actions != null && actions!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: actions!
                      .map((action) => Padding(
                            padding: const EdgeInsets.only(left: 8),
                            child: action,
                          ))
                      .toList(),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
