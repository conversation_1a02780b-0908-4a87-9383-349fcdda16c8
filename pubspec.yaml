name: cosmetic_basim
description: "تطبيق كوزمتك باسم لإدارة الديون والحسابات"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI and Icons
  cupertino_icons: ^1.0.8

  # State Management
  provider: ^6.1.2

  # Local Database
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Notifications (using flutter_local_notifications instead)
  flutter_local_notifications: ^17.2.3

  # Date and Time
  intl: ^0.20.0

  # Path and File handling
  path_provider: ^2.1.4
  device_info_plus: ^10.1.2

  # PDF Generation (for export feature)
  pdf: ^3.11.1

  # Utilities
  uuid: ^4.5.1

  # QR Code
  qr_flutter: ^4.1.0
  qr_code_scanner: ^1.0.1

  # Sharing
  share_plus: ^10.0.2

  # Image Picker
  image_picker: ^1.1.2

  # Permissions
  permission_handler: ^11.3.1
  shared_preferences: ^2.3.2

  # Cloud Storage & File Operations
  google_sign_in: ^6.2.1
  googleapis: ^13.2.0
  googleapis_auth: ^1.6.0
  file_picker: ^8.1.2
  open_file: ^3.5.7

  # Import/Export
  excel: ^4.0.6
  csv: ^6.0.0

  # Connectivity & Network
  connectivity_plus: ^6.0.5
  http: ^1.2.2

  # Audio
  audioplayers: ^6.1.0

  # PDF Generation & Printing
  printing: ^5.13.2



  # Camera & Image
  camera: ^0.10.6

  # Sharing
  url_launcher: ^6.3.1

  # Security & Authentication
  local_auth: ^2.3.0
  crypto: ^3.0.5
  encrypt: ^5.0.3
  flutter_secure_storage: ^9.2.2



dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting and Code Generation
  flutter_lints: ^5.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.13

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/sounds/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
