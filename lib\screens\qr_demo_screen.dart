import 'package:flutter/material.dart';
import '../utils/ui_enhancement_service.dart';
import 'simple_qr_display_screen.dart';
import 'advanced_qr_scanner_screen.dart';

/// شاشة تجريبية لنظام QR
class QRDemoScreen extends StatelessWidget {
  const QRDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UIEnhancementService.enhancedAppBar(
        title: 'نظام QR الذكي',
      ),
      body: UIEnhancementService.safeWrapper(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // عنوان رئيسي
              Text(
                'نظام QR الذكي المتطور',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              Text(
                'مشاركة ومسح إحصائيات المديونين بسهولة',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              // بطاقة إنشاء QR
              UIEnhancementService.enhancedCard(
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.qr_code,
                        size: 48,
                        color: Colors.green,
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    const Text(
                      'إنشاء QR للإحصائيات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    Text(
                      'أنشئ رمز QR يحتوي على ملخص شامل لجميع الديون والإحصائيات',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    UIEnhancementService.enhancedButton(
                      text: 'إنشاء QR ملخص',
                      onPressed: () => _navigateToQRDisplay(context, null),
                      icon: Icons.add_chart,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // بطاقة مسح QR
              UIEnhancementService.enhancedCard(
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.qr_code_scanner,
                        size: 48,
                        color: Colors.blue,
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    const Text(
                      'ماسح QR المتطور',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    Text(
                      'امسح رموز QR لعرض معلومات المديونين والإحصائيات بتقنية متطورة',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    UIEnhancementService.enhancedButton(
                      text: 'فتح الماسح',
                      onPressed: () => _navigateToQRScanner(context),
                      icon: Icons.camera_alt,
                      backgroundColor: Colors.blue,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // معلومات إضافية
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).primaryColor.withOpacity(0.1),
                      Theme.of(context).primaryColor.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).primaryColor.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).primaryColor,
                      size: 32,
                    ),
                    
                    const SizedBox(height: 12),
                    
                    const Text(
                      'مميزات نظام QR الذكي',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    _buildFeatureItem(
                      icon: Icons.security,
                      title: 'آمان وموثوقية',
                      description: 'تشفير متقدم للبيانات',
                    ),
                    
                    _buildFeatureItem(
                      icon: Icons.speed,
                      title: 'سرعة في المعالجة',
                      description: 'مسح وعرض فوري للمعلومات',
                    ),
                    
                    _buildFeatureItem(
                      icon: Icons.share,
                      title: 'مشاركة سهلة',
                      description: 'شارك الإحصائيات بنقرة واحدة',
                    ),
                    
                    _buildFeatureItem(
                      icon: Icons.analytics,
                      title: 'تفاصيل شاملة',
                      description: 'معلومات مفصلة عن كل مدين',
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // تعليمات الاستخدام
              UIEnhancementService.enhancedCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.help_outline,
                          color: Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'كيفية الاستخدام',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildInstructionStep(
                      number: '1',
                      title: 'إنشاء QR',
                      description: 'اضغط على "إنشاء QR ملخص" لإنشاء رمز يحتوي على الإحصائيات',
                    ),
                    
                    _buildInstructionStep(
                      number: '2',
                      title: 'مشاركة الرمز',
                      description: 'شارك الرمز مع الآخرين عبر التطبيقات المختلفة',
                    ),
                    
                    _buildInstructionStep(
                      number: '3',
                      title: 'مسح الرمز',
                      description: 'استخدم الماسح لقراءة رموز QR وعرض المعلومات',
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.green,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionStep({
    required String number,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.orange,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToQRDisplay(BuildContext context, dynamic debtor) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SimpleQRDisplayScreen(debtor: debtor),
      ),
    );
  }

  void _navigateToQRScanner(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AdvancedQRScannerScreen(),
      ),
    );
  }
}
